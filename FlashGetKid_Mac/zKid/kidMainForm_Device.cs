﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.Threading;
using System.Security.Cryptography;
using System.Drawing;
using System.Collections.Specialized;
using System.Xml;
using System.Diagnostics;
using System.Management;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Windows.Forms;

using iTong.CoreModule;
using iTong.CoreFoundation;
using iTong.Device;
using iTong.Android;

using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;

using skContextMenuStrip = iTong.CoreModule.skMenu;
using ToolStripMenuItem = iTong.CoreModule.skMenuItem;
using ToolStripItem = iTong.CoreModule.skMenuItem;
using PictureBox = iTong.CoreModule.skPictureBox;
using Label = iTong.CoreModule.skLabel;
using NotifyIcon = iTong.CoreModule.skStatusItem;
using ContentAlignment = iTong.CoreModule.ContentAlignment;

namespace iTong.CoreModule
{
    public partial class kidMainForm
    {
        private MobileDeviceManager mDevMgr = null;
        private skButton btnEnterMonitor;
        private skButton btnExitMonitor;
        private skLabel lblProgress;
        private skTextBox txtCode;
        private skButton btnFamiSafe;
        private iPhoneDevice mDevice = null;
        private iPhoneDevice mPreviousDevice = null;

        private ConnectionChangedEventArgs mConnectionChangedEventArgs;

        public void StartListen()
        {
            string str = string.Format("{0}{1},Soft={2}",
                                       Common.GetMacOSName(),
                                       Common.GetOSVersion(),
                                       Common.GetSoftVersion());

            Common.LogException(str, "StartListen of tbMainView");

            try
            {
                mDevMgr = MobileDeviceManager.Instance();

                mDevMgr.ConnectionChanged += new EventHandler<ConnectionChangedEventArgs>(this.OnDeviceConnectionChanged);
                mDevMgr.RecoveryConnectionChanged += new EventHandler<RecoveryConnectionChangedEventArgs>(this.OnDeviceRecoverConnectionChanged);
                mDevMgr.DFUConnectionChanged += new EventHandler<DFUConnectionChangedEventArgs>(this.OnDeviceDUFConnectionChanged);
                mDevMgr.ConnectFailed += new EventHandler<ConnectFailedEventArgs>(this.OnDeviceConnectionFailed);

                mDevMgr.StartListen();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "StartListen");
            }
        }

        private void OnDeviceConnectionChanged(object sender, ConnectionChangedEventArgs e)
        {
            mConnectionChangedEventArgs = e;
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new EventHandler<ConnectionChangedEventArgs>(this.OnDeviceConnectionChanged), sender, e);
            }
            else
            {
                this.lblVersion.Visible = false;
                this.lblVersion.skTextColor = Color.Blue;
                this.lblVersion.skTextAlign = ContentAlignment.MiddleCenter;
                this.lblVersion.skText = string.Format("{0}({1}) - 已{2}", SummaryInfo.FormatProduct(e.Device.ProductType), e.Device.ProductVersion, e.Device.IsConnected ? "连接" : "断开");
                this.lblVersion.BringToFront();

                this.mPreviousDevice = this.mDevice;

                if (this.mDevMgr != null && this.mDevMgr.ConnectedDevices.Count >= 1)
                {
                    foreach (iPhoneDevice device in this.mDevMgr.ConnectedDevices)
                    {
                        if (device.IsConnected)
                        {
                            this.mDevice = device;
                            break;
                        }
                    }
                }
                else
                {
                    if (this.mDevMgr != null && this.mDevMgr.ConnectedErrorDevices.Count >= 1)
                    {
                        this.mDevice = e.Device;
                    }
                    else
                    {
                        this.mDevice = null;
                    }
                }

                //if (this.mSelectDeviceMode == SelectDeviceMode.Normal)
                //{
                //    this.mDevice = e.Device;
                //}
                //else
                //{
                //    if (this.mDevMgr != null && this.mDevMgr.ConnectedDevices.Count >= 1)
                //    {
                //        foreach (iPhoneDevice device in this.mDevMgr.ConnectedDevices)
                //        {
                //            if (device.IsConnected)
                //            {
                //                this.mDevice = device;
                //                break;
                //            }
                //        }
                //    }
                //    else
                //    {
                //        this.mDevice = e.Device;
                //    }

                //    //已选开启/关闭监督模式，不替换设备
                //    //if (this.mDevice == null || this.mDevice.DeviceID.Equals(e.Device.DeviceID))
                //    //    this.mDevice = e.Device;
                //}

                if (this.mPreviousDevice != null && this.mDevice != null && this.mPreviousDevice.DeviceID.Equals(this.mDevice.DeviceID))
                    return;

                if (this.mSelectDeviceMode == SelectDeviceMode.Activating || this.mSelectDeviceMode == SelectDeviceMode.Deactivating)
                {
                    //if (this.mPreviousDevice != null && this.mDevice != null && this.mPreviousDevice.DeviceID.Equals(this.mDevice.DeviceID))
                    //    return;
                    //else
                        this.DeviceConnectHandle();
                }
                else
                {
                    this.DeviceConnectHandle();
                }

                //this.skTitle = string.Format("{0} - 已{1}", e.Device.ProductType, e.Device.DeviceName, e.Device.IsConnected ? "连接" : "断开");

                //this.lblInTestMode.skText = this.lblVersion.skText;
            }
        }

        private void OnDeviceRecoverConnectionChanged(object sender, RecoveryConnectionChangedEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new EventHandler<RecoveryConnectionChangedEventArgs>(this.OnDeviceRecoverConnectionChanged), sender, e);
            }
            else
            {
            }
        }

        private void OnDeviceDUFConnectionChanged(object sender, DFUConnectionChangedEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new EventHandler<DFUConnectionChangedEventArgs>(this.OnDeviceDUFConnectionChanged), sender, e);
            }
            else
            {
            }
        }

        private void OnDeviceConnectionFailed(object sender, ConnectFailedEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new EventHandler<ConnectFailedEventArgs>(this.OnDeviceConnectionFailed), sender, e);
                return;
            }

            try
            {
                iPhoneDevice device = (iPhoneDevice)sender;

                Common.LogException("ConnectFailed: " + device.DeviceName + "\t" + device.ProductVersion + "\t" + ((int)e.ErrorCode).ToString());
                string text = string.Empty;
                kAMDError errorCode = e.ErrorCode;

                if (errorCode == kAMDError.kAMDPasswordProtectedError)
                {
                    text = string.Format(base.Language.GetString("Main.Message.ShouldTypePwd"), device.DeviceName);
                    if (Conversion.Val(device.ProductVersion.Substring(0, 1)) >= 7.0)
                    {
                        text = string.Format(base.Language.GetString("Main.Message.TrustPC"), device.DeviceName);
                    }
                }
                else if (errorCode == kAMDError.kAMDTrustComputerError) //未信任
                {
                    text = string.Format(base.Language.GetString("Main.Message.TrustPC"), device.DeviceName);
                }
                else
                {
                    text = base.Language.GetString("Main.Message.DeviceIsLocked");
                }
            }
            catch (Exception ex)
            {
                this.InitPanel(this.pnlConnectionFailed, true);

                Common.LogException(ex.ToString(), "tbMainView.OnDeviceConnectionFailedBeginInvoke");
            }
        }

        #region 早期的测试代码

        private bool mblnShowTrust = false;
        private void ShowTrust(ConnectFailedEventArgs args, string text)
        {
            if (this.mblnShowTrust)
                return;

            this.mblnShowTrust = true;

            if (skMessageBox.Show(this, text, this.Language.GetString("Parent.Tabbar.Device.Notice"), MessageBoxButtons.RetryCancel) == DialogResult.Retry)
            {
                args.Retry = true;
            }

            this.mblnShowTrust = false;
        }

        private Color colorBG = skColor.FromArgb(0xec, 0xf0, 0xf4);
        private Color colorBGDark = skColor.FromArgb(0xdc, 0xe0, 0xe4);
        private Color colorBGLight = skColor.FromArgb(0xe6, 0xea, 0xee);
        private Color colorBlack = skColor.FromArgb(0x33, 0x37, 0x3b);
        private Color colorBlue = skColor.FromArgb(0x3c, 0x77, 0xff);
        private Color colorBlueHover = skColor.FromArgb(0x1b, 0x52, 0xcc);
        private Color colorHover = skColor.FromArgb(217, 227, 245);
        private Color colorDown = skColor.FromArgb(200, 216, 246);
        private Size sizeButton = new Size(80, 28);
        private Padding padding = new Padding(16, 24, 15, 15);
        private int diffX = 15;
        private int diffY = 20;

        private skButton CreateButton(string strText, EventHandler handler, int width = 0, int height = 0)
        {
            skButton btn = new skButton();

            if (width == 0)
                width = 200;

            if (height == 0)
                height = 80;

            btn = new skButton();
            this.Controls.Add(btn);
            btn.Margin = new Padding(5);
            btn.Size = new Size(width, height);
            btn.skUpToDownWhenCenter = true;
            btn.skIconBeforeText = true;
            btn.skIconPlaceText = 1;
            //btn.skIconSize = new Size(56, 56);
            btn.skIconAlign = skImageAlignment.Center;
            //btn.skIcon = MyResource.GetImage(string.Format("map_{0}_4.png", type.ToString().ToLower()));
            btn.skIconState = skImageState.FourState;

            btn.skTextFont = MyFont.CreateFont(12, true);
            btn.skText = strText;
            btn.skTextAlign = ContentAlignment.MiddleCenter;
            btn.skTextColor = colorBlue;
            btn.skTextColorHover = colorBlue;

            btn.skBorderType = skBorderType.Round;
            btn.skBorderRadius = new skBorderRadius(10);
            btn.skBackgroundColor = skColor.Transparent;
            btn.skBorderStrokeColor = colorBG;
            //btn.skBorderStrokeColorHover = colorBlue;
            btn.skBorderFillColor = colorBG;
            btn.skBorderFillColorHover = colorHover;
            btn.skBorderFillColorDown = colorDown;

            btn.skShowToolTipOnButton = false;
            btn.Anchor = AnchorStyles.Top;

            btn.Click += handler;

            return btn;
        }

        private void InitMonitorButton()
        {
            this.btnEnterMonitor = this.CreateButton("进入监督模式", this.btnEnterMonitor_Click);
            this.btnExitMonitor = this.CreateButton("退出监督模式", this.btnExitMonitor_Click);

            this.btnFamiSafe = this.CreateButton("使用FamiSafe的URL", null, 150, 30);
            this.btnFamiSafe.skButtonStyle = skButtonStyle.Check;
            this.btnFamiSafe.skAutoSize = true;
            this.btnFamiSafe.skBackgroundColor = skColor.Transparent;
            this.btnFamiSafe.skBorderType = skBorderType.None;
            //this.btnFamiSafe.skBorderFillColor = Color.Transparent;

            this.lblProgress = new skLabel();
            this.Controls.Add(this.lblProgress);
            this.lblProgress.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top;
            this.lblProgress.skTextFont = MyFont.CreateFont(10, false);
            this.lblProgress.skText = "";
            this.lblProgress.skTextAlign = ContentAlignment.MiddleCenter;
            this.lblProgress.skTextColor = colorBlue;
            this.lblProgress.skTextColorHover = colorBlue;
            this.lblProgress.skBorderType = skBorderType.Round;
            this.lblProgress.skBorderRadius = new skBorderRadius(10);
            this.lblProgress.skBackgroundColor = skColor.Transparent;
            this.lblProgress.skBorderStrokeColor = colorBG;
            this.lblProgress.skBorderStrokeColorHover = colorBlue;
            this.lblProgress.skBorderFillColor = colorBG;
            this.lblProgress.skBorderFillColorHover = colorHover;
            this.lblProgress.skBorderFillColorDown = colorDown;
            this.lblProgress.skAutoHeight = true;

            this.txtCode = new skTextBox();
            this.Controls.Add(this.txtCode);
            this.txtCode.Size = new Size(150, 30);
            this.txtCode.PlaceHolder = "请输入配对码";
            this.txtCode.Font = MyFont.CreateFont(10, true);

            int top = this.lblVersion.Bottom + 20;
            int left = (this.Width - (this.btnEnterMonitor.Width + 20 + this.btnExitMonitor.Width)) / 2;
            this.btnEnterMonitor.Location = new Point(left, top);
            this.btnExitMonitor.Location = new Point(this.btnEnterMonitor.Right + 20, top);

            //this.txtCode.Location = new Point((this.Width - (this.txtCode.Width)) / 2, this.btnExitMonitor.Bottom + 20);
            this.txtCode.Location = new Point(btnEnterMonitor.Left, this.btnExitMonitor.Bottom + 20);
            this.btnFamiSafe.Location = new Point(this.btnExitMonitor.Left, this.txtCode.Top + (this.txtCode.Height - this.btnFamiSafe.Height) / 2);

            this.lblProgress.Location = new Point(this.btnEnterMonitor.Left, this.txtCode.Bottom + 20);
            this.lblProgress.Size = new Size(this.btnExitMonitor.Right - this.btnEnterMonitor.Left, this.btnEnterMonitor.Height);
        }

        private void btnEnterMonitor_Click(object sender, EventArgs e)
        {
            ThreadMgr.Start(() => {
                this.SetControlEnabled(false);
                this.MonitorAction(true);
                this.SetControlEnabled(true);
            });
        }

        private void btnExitMonitor_Click(object sender, EventArgs e)
        {
            ThreadMgr.Start(() => {
                this.SetControlEnabled(false);
                this.MonitorAction(false);
                this.SetControlEnabled(true);
            });
        }

        private delegate void SetControlEnabledHanlder(bool blnEnabled);
        private void SetControlEnabled(bool blnEnabled)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new SetControlEnabledHanlder(this.SetControlEnabled), blnEnabled);
            }
            else
            {
                this.btnEnterMonitor.Enabled = blnEnabled;
                this.btnExitMonitor.Enabled = blnEnabled;
                this.txtCode.Enabled = blnEnabled;

                if (!blnEnabled)
                {
                    this.lblProgress.skText = string.Empty;
                }
            }
        }

        private void MonitorAction(bool blnEnter)
        {
            bool isFamiSafe = false;
            string pathFamiSafe = Path.Combine(Folder.AppFolder, "FamiSafe.txt");
            string urlFamiSafe = string.Empty;

            if (this.btnFamiSafe.skChecked)
                isFamiSafe = true;

            if (File.Exists(pathFamiSafe))
            {
                isFamiSafe = true;
                urlFamiSafe = File.ReadAllText(pathFamiSafe);
            }

            ServerArgs<KidProfileInfo> args = null;
            if (blnEnter && !isFamiSafe)
            {
                string strCode = this.txtCode.Text.Replace(" ", "");
                if (!string.IsNullOrEmpty(strCode))
                {
                    args = FGKidAPI.GetProfile(strCode);
                    if (args == null)
                    {
                        skSplashBox.Show("GetProfile 返回值空，请检查网络！", this);
                        return;
                    }

                    if (args.Data == null || args.Code != 1)
                    {
                        skSplashBox.Show(string.Format("GetProfile 返回值异常: code={0}, errMsg={1}", args.Code, args.Msg), this);
                        return;
                    }

                    if (string.IsNullOrEmpty(args.Data.url))
                    {
                        skSplashBox.Show(string.Format("GetProfile 返回值异常 Url为空: code={0}, errMsg={1}", args.Code, args.Msg), this);
                        return;
                    }
                }
            }

            if (this.mDevMgr.ConnectedDevices.Count == 0)
            {
                skSplashBox.Show("请插入iPhone", this);
                return;
            }

            iPhoneDevice dev = this.mDevMgr.ConnectedDevices[0];
            if (dev.FindMyPhone)
            {
                skSplashBox.Show("请关闭Find My iPhone", this);
                return;
            }


            BackupRestoreEventArgs backupEvent = new BackupRestoreEventArgs();

            string dirBackup = Folder.GetDesktopPath("Restore");
#if DEBUG
            //dirBackup = Path.Combine(dirBackup,string.Format("{0}_{1}", dev.ProductType, DateTime.Now.ToString("yyyyMMdd_HHmmss")));
#endif

            bool blnResult = false;
            byte[] arrDbData = MyResource.GetData("Manifest.db");
            if (blnEnter)
            {
                MDMConfig config = null;

                if (!isFamiSafe)
                {
                    if (args == null)
                    {
                        config = new MDMConfig()
                        {
                            ConfigurationURL = "http://test-api.flashget.com/pc/kids/download?downloadProfile?q=A2EA2BD8F57B9DD6EB9318E1B8A495C6C98F2B78FE9F0E3AEBD93C9A1B5BE34A5CD3B7EDFC484A72F5805DA6BC340806700E236BC9272024",
                            OrganizationEmail = "<EMAIL>",
                            OrganizationAddress = "Sand Studio Singapro",
                            OrganizationName = "AirDroid Parental Control",
                            OrganizationPhone = "+86 10086",
                        };
                    }
                    else
                    {
                        config = new MDMConfig()
                        {
                            ConfigurationURL = args.Data.url,
                            OrganizationEmail = args.Data.mail,
                            OrganizationAddress = "Sand Studio Singapro",
                            OrganizationName = "AirDroid Parental Control",
                            OrganizationPhone = "+86 10086",
                        };
                    }
                }
                else
                {
                    config = new MDMConfig()
                    {
                        ConfigurationURL = @"https://app-api-pro.famisafe.com/v1/mdm/download?s=_GXw07cwd75VqxuxNf4fCN-dePjZEUo5jkGjEdVEoujEo00-5YfnIJm1A2ck5lRPx9FvYw-cRufbA92x9DnnwedgCDh75UXeIezlvLhDT3tHzI9RDJAjzBUUCmUnG2Tr",
                        OrganizationEmail = "<EMAIL>",
                        OrganizationAddress = "WS",
                        OrganizationName = "FamiSafe",
                        OrganizationPhone = "************",
                    };

                    if (!string.IsNullOrEmpty(urlFamiSafe))
                        config.ConfigurationURL = urlFamiSafe;
                }

                blnResult = mbdb.MDM_CreateBackupFiles(dirBackup, dev, config, arrDbData, backupEvent);
            }
            else
            {
                blnResult = mbdb.MDM_CreateBackupFiles(dirBackup, dev, null, arrDbData, backupEvent);
            }

            //CreatePlist(@"d:\Users\Administrator\Desktop\Air\Backup\install_doRestore_00008030-000A31061188802E");
            //CreatePlist(@"d:\Users\Administrator\Desktop\Air\Backup\remove_doRestore_00008030-000A31061188802E");
            //CreatePlist(dirBackup);


            if (blnResult)
            {
                RestoreOptions options = RestoreOptions.RestorePreserveSettings;

                dev.mb2_Restore(dirBackup, this.OnRestoreCallbackOld, options, string.Empty);
            }
            else
            {
                this.OnRestoreCallbackOld(backupEvent);
            }
        }

        public static void CreatePlist(string dirBackup)
        {
            string strDB = Path.Combine(dirBackup, "Manifest.db");
            List<MBFileRecord> list = mbdb.ReloadDB(strDB, dirBackup, true);
            foreach (MBFileRecord item in list)
            {
                if (item.fileInfoPlist == null)
                    continue;

                string pathSave = Path.Combine(dirBackup, item.fileID + ".plist");
                File.WriteAllBytes(pathSave, item.fileInfoPlist);

                string strPlist = iTong.Device.CoreFoundation.ReadPlist(item.fileInfoPlist);
                File.WriteAllText(pathSave.Replace(".plist", "_text.plist"), strPlist, Encoding.UTF8);
            }
        }

        private void OnRestoreCallbackOld(BackupRestoreEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new BackupRestoreHandler(this.OnRestoreCallbackOld), e);
            }
            else
            {
                Console.WriteLine("Restore Progress: " + e.Progress.ToString("0.00#"));

                if (e.Completed)
                {
                    this.lblProgress.skTextColor = Color.Blue;
                    this.lblProgress.skText = "还原成功";

                    if (this.mDevMgr.ConnectedDevices.Count > 0)
                    {
                        //iPhoneDevice dev = this.mDevMgr.ConnectedDevices[0];
                        //dev.Restart();
                    }
                }
                else if (!string.IsNullOrEmpty(e.ErrorMsg))
                {
                    this.lblProgress.skTextColor = Color.Red;
                    this.lblProgress.skText = "还原失败：" + e.ErrorMsg;
                }
                else
                {
                    this.lblProgress.skTextColor = Color.Blue;
                    this.lblProgress.skText = string.Format("正在还原：{0}%", e.Progress.ToString("0.00#"));
                }
            }
        }

        #endregion
    }
}
