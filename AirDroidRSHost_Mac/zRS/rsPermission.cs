﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using AVFoundation;
using iTong.Android;
using iTong.CoreFoundation;

using Color = AppKit.NSColor;
using Point = CoreGraphics.CGPoint;
using Size = CoreGraphics.CGSize;

namespace iTong.CoreModule
{
    /// <summary>
    /// 权限管理辅助类
    /// </summary>
    public static class PermissionHelper
    {
        /// <summary>
        /// 设置权限请求标记
        /// </summary>
        public static void SetPermissionRequestFlag()
        {
            try
            {
                SettingMgr.SetValue("PermissionRequesting", DateTime.Now.Ticks.ToString());
                Common.Log("Permission request flag set", "PermissionHelper");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "SetPermissionRequestFlag");
            }
        }

        /// <summary>
        /// 清除权限请求标记
        /// </summary>
        public static void ClearPermissionRequestFlag()
        {
            try
            {
                SettingMgr.SetValue("PermissionRequesting", "");
                Common.Log("Permission request flag cleared", "PermissionHelper");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ClearPermissionRequestFlag");
            }
        }
    }
    public partial class rsPermission : frmBase
    {
        #region Constants
        private const int DEFAULT_FORM_HEIGHT = 664;
        private const int DEFAULT_PANEL_HEIGHT = 77;
        private const int DEFAULT_PANEL_WIDTH = 540;
        private const int DEFAULT_FORM_WIDTH = 600;
        private const int DEFAULT_PADDING_LEFT = 30;
        private const string OS_VERSION_THRESHOLD = "10.15";
        #endregion

        #region Properties
        public override int skTitleBarHeight => 30;
        public override int skStatusBarHeight => 0;
        public override skSplit skTransparentImageSplit => new skSplit(15);
        #endregion

        #region Enums
        private enum PermissionType
        {
            ScreenCapture,
            Accessibility,
            AllFiles,
            Microphone
        }
        #endregion

        #region Fields
        private readonly tdActionHelper<tdActionItemForRS> mActionHelper = tdActionHelper<tdActionItemForRS>.Instance();
        private int mFormHeight = DEFAULT_FORM_HEIGHT;

        private bool mIsFirstInstall = false;
        private bool mHasScreenCapture = false;
        private bool mHasAccessibility = false;
        private bool mHasAllFiles = false;
        private bool mHasMicrophone = false;

        #endregion

        #region Constructor
        public rsPermission(bool isFirstInstall = false)
        {
            this.mIsFirstInstall = isFirstInstall;
            this.CheckPermissions();

            this.InitializeComponent();
            this.InitLangage();
            this.InitFrame();
        }
        #endregion

        #region Permission Management


        private void CheckPermissions()
        {
            this.mHasScreenCapture = MyAPI.AuthStatusForScreenCapture;  //是否有屏幕录制权限
            this.mHasAccessibility = MyAPI.AuthStatusForAccessibility; //是否有辅助功能权限
            this.mHasAllFiles = MyAPI.AuthStatusForAllFiles; //是否有完全磁盘访问权限
            this.mHasMicrophone = MyAPI.AuthStatusForMicrophone; //是否有麦克风权限
        }
       
        #endregion

        #region UI Initialization
        protected void InitFrame()
        {
            try
            {
                MyAPI.LoadAccesssPower();
                int removeControlsCount = CalculateRemoveControlsCount();
                InitializeFormLayout(removeControlsCount);
                InitializePanelLayouts();
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "InitFrame");
            }
        }

        private int CalculateRemoveControlsCount()
        {
            int count = 0;

            if (Common.OSVersion < new Version(OS_VERSION_THRESHOLD))
            {
                pnlNeed.Controls.Remove(pnlScreenRecording);
                pnlNeed.Size = new Size(DEFAULT_PANEL_WIDTH, DEFAULT_PANEL_HEIGHT);
                count++;
            }

            if (mIsFirstInstall)
            {
                pnlGeneral.Controls.Remove(pnlOther);
                btnFinshOrClose.Enabled = this.mHasScreenCapture;
                count += 2;
            }

            return count;
        }

        private void InitializeFormLayout(int removeControlsCount)
        {
            Size = new Size(DEFAULT_FORM_WIDTH, mFormHeight - DEFAULT_PANEL_HEIGHT * removeControlsCount);
            InitializeGeneralPanel();
            InitializeDescribeLabel();
            SetGuideImage();
        }

        private void InitializeGeneralPanel()
        {
            pnlGeneral.Location = new Point(0, 0);
            pnlGeneral.Size = new Size(Width, Height - skTitleBarHeight);
            pnlGeneral.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom;
            pnlGeneral.skBorderType = skBorderType.None;
            pnlGeneral.skBorderStrokeColor = Color.White;
            pnlGeneral.skBackgroundColor = Color.White;
            pnlGeneral.Padding = new Padding(0, 25, 0, 10);
        }

        private void InitializeDescribeLabel()
        {
            lblDescribe.Size = new Size(Width - 40, 22);
            lblDescribe.Margin = new Padding(DEFAULT_PADDING_LEFT, 0, 0, 0);
        }

        private void SetGuideImage()
        {
            Version version = Common.OSVersion;
            picGuide.Image = version.Major >= 13
                ? MyResource.GetImage("pic_permission_high_version.png")
                : MyResource.GetImage("pic_permission_low_version.png");

            MyLog.WriteLine($"rsPermission -> OSVersion: {version}");
            picGuide.Margin = new Padding((pnlGeneral.Width - picGuide.Width) / 2, 5, 0, 5);
        }

        private void InitializePanelLayouts()
        {
            pnlNeed.Margin = new Padding(DEFAULT_PADDING_LEFT, 10, 0, 0);
            pnlOther.Margin = new Padding(DEFAULT_PADDING_LEFT, 0, 0, 0);

            InitPanel(pnlScreenRecording, picScreenIcon, lblScreenTitle, btnScreenTip, lblScreenDescribe, btnScreenAllow);
            InitPanel(pnlAccessibility, picAccessibilityIcon, lblAccessibilityTitle, btnAccessibilityTip, lblAccessibilityDescribe, btnAccessibilityAllow);
            InitPanel(pnlAllFiles, picAllFilesIcon, lblAllFilesTitle, null, lblAllFilesDescribe, btnAllFilesAllow);
            InitPanel(pnlMicrophone, picMicrophoneIcon, lblMicrophoneTitle, null, lblMicrophoneDescribe, btnMicrophoneAllow);

            int btnPaddingLeft = (pnlGeneral.Width - btnFinshOrClose.Width) / 2;
            btnFinshOrClose.Margin = new Padding(btnPaddingLeft, 0, 0, 0);
        }

        private void InitPanel(skPanel panel, skPictureBox picIcon, skLabel lblTitle, skButton btnTip, skLabel lblDescribe, skButton btnAllow)
        {
            lblTitle.Location = new Point(picIcon.Width + 10, panel.Height - lblTitle.Height);

            if (btnTip != null)
                btnTip.Location = new Point(lblTitle.Right + 5, lblTitle.Top + (lblTitle.Height - btnTip.Height) / 2 + 2);

            lblDescribe.Location = new Point(lblTitle.Left, lblTitle.Top - 5 - lblDescribe.Height);
            picIcon.Location = new Point(0, panel.Height - picIcon.Height - (lblTitle.Height + lblDescribe.Height + 5 - picIcon.Height) / 2);
            btnAllow.Location = new Point(picIcon.Width + lblDescribe.Width + (panel.Width - picIcon.Width - lblDescribe.Width - btnAllow.Width), picIcon.Top + (picIcon.Height - btnAllow.Height) / 2);
        }

        private void InitButton(skButton btn, bool isPermission)
        {
            if (isPermission)
            {
                btn.skText = "";
                btn.skShowIcon = true;
                btn.skBackgroundImage = null;
                btn.Enabled = false;
                btn.Cursor = Cursors.Default;
                btn.skUseHandCursor = false;
            }
            else
            {
                btn.skText = this.Language.GetString("permission_dialog_allow"); //允许
                btn.skShowIcon = false;
                btn.skBackgroundImage = MyResource.GetImage("rs_btn_blue_4.png");
                btn.Enabled = true;
                btn.Cursor = Cursors.Hand;
                btn.skUseHandCursor = true;
            }

            if (btn == btnScreenAllow && mIsFirstInstall)
                btnFinshOrClose.Enabled = isPermission;
        }

        private void InitLangage()
        {
            skTitle = this.Language.GetString("rs_mac_access_permission_settings"); //访问权限设置
            skShowTitle = true;

            lblDescribe.skText = this.Language.GetString("rs_mac_permission_grant_prompt"); //请授予 AirDroid Remote Support 以下权限，以使用远程访问功能。
            btnFinshOrClose.skText = mIsFirstInstall
                ? this.Language.GetString("Common_Complete") //完成
                : this.Language.GetString("Common.Button.Close"); //关闭

            lblScreenTitle.skText = this.Language.GetString("rs_mac_screen_recording_title"); //屏幕录制
            lblScreenDescribe.skText = this.Language.GetString("rs_mac_screen_recording_permission"); //允许在连接此 Mac 时共享您的屏幕

            lblAccessibilityTitle.skText = this.Language.GetString("rs_mac_accessibility_title"); //辅助功能
            lblAccessibilityDescribe.skText = this.Language.GetString("rs_mac_accessibility_permission"); //允许在连接此 Mac 时控制您的鼠标和键盘

            lblAllFilesTitle.skText = this.Language.GetString("rs_mac_full_disk_access_title"); //完全磁盘访问
            lblAllFilesDescribe.skText = this.Language.GetString("rs_mac_full_disk_access_permission"); //允许在连接此 Mac 时与进行文件传输

            lblMicrophoneTitle.skText = this.Language.GetString("rs_mac_microphone_title"); //麦克风
            lblMicrophoneDescribe.skText = this.Language.GetString("rs_mac_microphone_permission"); //允许在连接此 Mac 时进行语音通话

            string strNeed = this.Language.GetString("rs_mac_required_remark"); //必需
            btnScreenTip.skText = strNeed;
            btnAccessibilityTip.skText = strNeed;

            SetBtnAllowState();
        }
        #endregion

        #region Event Handlers

        private bool mHasTccRequestScreenCapture = false;

        private bool mHasTccRequestAccessibility = false;

        private bool mHasTccRequestAllFiles = false;

        private bool mHasTccRequestMicrophone = false;

        private void btnFinshOrClose_Click(object sender, EventArgs e)
        {
            SettingMgr.SetValue(KeyNameForRS.NotFirstInstall, true);
            Close();
        }

        /// <summary>允许 屏幕录制</summary>
        private void btnScreenAllow_Click(object sender, EventArgs e)
        {
            // 设置权限请求标记
            PermissionHelper.SetPermissionRequestFlag();

            if (!this.mHasTccRequestScreenCapture)
            {
                this.mHasTccRequestScreenCapture = true;
                MyAPI.TCCAccessRequest(TCCServiceType.kTCCServiceScreenCapture);
            }

            MyAPI.RequestAccessPower(TCCServiceType.kTCCServiceScreenCapture);
        }

        /// <summary>允许 辅助功能</summary>
        private void btnAccessibilityAllow_Click(object sender, EventArgs e)
        {
            // 设置权限请求标记
            PermissionHelper.SetPermissionRequestFlag();

            if (!this.mHasTccRequestAccessibility)
            {
                this.mHasTccRequestAccessibility = true;
                MyAPI.CheckHasAccessibility(true);
            }

            MyAPI.RequestAccessPower(TCCServiceType.kTCCServiceAccessibility);
        }

        /// <summary>允许 完全磁盘访问</summary>
        private void btnAllFilesAllow_Click(object sender, EventArgs e)
        {
            // 设置权限请求标记
            PermissionHelper.SetPermissionRequestFlag();

            if (!this.mHasTccRequestAllFiles)
            {
                this.mHasTccRequestAllFiles = true;
                MyAPI.TCCAccessRequest(TCCServiceType.kTCCServiceSystemPolicyAllFiles);
            }

            MyAPI.RequestAccessPower(TCCServiceType.kTCCServiceSystemPolicyAllFiles);
        }

        /// <summary>允许 麦克风</summary>
        private void btnMicrophoneAllow_Click(object sender, EventArgs e)
        {
            MyAPI.TCCAccessRequest(TCCServiceType.kTCCServiceMicrophone);
            MyAPI.OpenSettingForMicrophone();
            //MyAPI.RequestAccessPower(TCCServiceType.kTCCServiceMicrophone);
        }

        private void OnAccessPowerCallback(object sender, AccessPowerEventArg e)
        {
            GetPerssionsTimerHandle();
        }

        private void GetPerssionsTimerHandle()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(GetPerssionsTimerHandle));
                return;
            }

            CheckPermissions();
            SetBtnAllowState();
        }

        private void SetBtnAllowState()
        {
            this.InitButton(this.btnScreenAllow, this.mHasScreenCapture);
            this.InitButton(this.btnAccessibilityAllow, this.mHasAccessibility);
            this.InitButton(this.btnAllFilesAllow, this.mHasAllFiles);
            this.InitButton(this.btnMicrophoneAllow, this.mHasMicrophone);
        }
        #endregion

        #region Override Methods
        protected override void InitControls()
        {
            base.InitControls();

            FormBorderStyle = FormBorderStyle.None;
            skBorderRadius = new skBorderRadius(6);
            skBorderStrokeColor = Color.White;
            skBorderWidth = 0;

            Icon = MyResource.GetImage("airdroid.png");
            skIcon = MyResource.GetImage("airdroid_48.png");

            ShowIcon = true;
            skIconSize = Size.Empty;
            mIconPadding = new Padding(20, -2, 0, 0);
            skBackgroundColor = Color.White;

            pnlGeneral.AutoLayout();
            SetPanelVisible(pnlGeneral);

            MyForm.SetParentCenter(this, MyForm.GetMainForm());
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            SetPanelVisible(pnlGeneral);
            MyAPI.AccessPowerCallback += OnAccessPowerCallback;
        }

        private void SetPanelVisible(Control pnl)
        {
            if (pnl == null)
                return;

            foreach (Control control in this.Controls)
            {
                if (control is skPanel)
                {
                    control.Visible = (control == pnl);
                }
            }

            pnl.Visible = true;
            pnl.BringToFront();
        }

        public override void OnWindowBecomeKey(object sender, EventArgs e)
        {
            base.OnWindowBecomeKey(sender, e);
            MyAPI.LoadAccesssPower();
        }

        public override void OnWindowWillClose(object sender, EventArgs e)
        {
            MyAPI.AccessPowerCallback -= OnAccessPowerCallback;
            base.OnWindowWillClose(sender, e);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            if (!this.mHasScreenCapture && this.mIsFirstInstall)
                SocketMgr.KillProcessById(SocketMgr.GetAppId(SocketType.UserDisplay));
            else
                SettingMgr.SetValue(KeyNameForRS.NotFirstInstall, true);
        }
        #endregion
    }
}