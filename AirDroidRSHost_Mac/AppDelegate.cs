using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel;
using System.IO;
using System.Diagnostics;

using AppKit;
using Foundation;
using ObjCRuntime;
using CoreGraphics;


using iTong.Android;
using iTong.CoreFoundation;
using iTong.CoreModule;
using System.Windows.Forms;

namespace AirDroidRemoteSupportHost
{
	/// <summary>
	/// 应用退出原因枚举
	/// </summary>
	public enum ExitReason
	{
		/// <summary>用户主动退出</summary>
		UserExit,
		/// <summary>系统权限重启</summary>
		SystemPermissionRestart
	}

	//public static class AEKeyword
	//{
	//	public static readonly uint DirectObject = Create("----");
	//	public static readonly uint ErrorNumber = Create("errn");
	//	public static readonly uint ErrorString = Create("errs");
	//	public static readonly uint ProcessSerialNumber = Create("psn ");
	//	public static readonly uint PreDispatch = Create("phac");
	//	public static readonly uint SelectProc = Create("selh");
	//	public static readonly uint AERecorderCount = Create("recr");
	//	public static readonly uint AEVersion = Create("vers");

	//	private static uint Create(string key)
	//	{
	//		return (((uint)key[0]) << 24 |
	//				((uint)key[1]) << 16 |
	//				((uint)key[2]) << 8 |
	//				((uint)key[3]) << 0);
	//	}
	//}

	public partial class AppDelegate : NSApplicationDelegate
	{

        protected skWindow MainWindow { get; set; }

        #region 权限状态存储

        /// <summary>应用启动时的权限状态</summary>
        private static bool? mStartupScreenPermission = null;
        private static bool? mStartupAccessibilityPermission = null;
        private static bool? mStartupDiskPermission = null;
        private static bool? mStartupMicrophonePermission = null;

        /// <summary>上次保存的权限状态</summary>
        private const string PERM_SCREEN_KEY = "LastScreenPermission";
        private const string PERM_ACCESSIBILITY_KEY = "LastAccessibilityPermission";
        private const string PERM_DISK_KEY = "LastDiskPermission";
        private const string PERM_MICROPHONE_KEY = "LastMicrophonePermission";
        private const string APP_START_TIME_KEY = "AppStartTime";

        #endregion

		public static AppDelegate Instance
		{
			get
			{
				return NSApplication.SharedApplication.Delegate as AppDelegate;
			}
		}

		public void ExitApp()
		{
			// 设置用户主动退出标记
			iTong.CoreModule.PermissionHelper.SetUserExitFlag();
			NSApplication.SharedApplication.Terminate(this);
		}

		public override void DidFinishLaunching (NSNotification notification)
		{
            //通过WebView的策略控制来实现URL跳转打开

            // 检查应用重启能力
            CheckAppRestartability();

            // 记录应用启动时间和权限状态
            RecordStartupState();

            string[] arrPara = ParaMgr.Arguments;
            if (arrPara == null || arrPara.Length == 0)
                return;

            System.Windows.Forms.ExtendControl.IsWindowAnchor = true;

            switch (arrPara[0])
            {
                case ParaMgr.CommandRunWidnows:
                    {
                        rsMainForm view = new rsMainForm();
                        view.Show();

                        //保存MainWindow
                        this.MainWindow = (skWindow)view.Window;

                        break;
                    }

                case ParaMgr.CommandRunSafeMode:
                    {
                        frmSafeMode.Init();
                        break;
                    }
            }
        }

        /// <summary>
        /// 协议：weixin://, tongbu:// 避免WebKit弹出窗体(需要设置info.plist中的URL types的URL Schemes
        /// </summary>
        /// <param name="descriptor">Descriptor.</param>
        /// <param name="replyEvent">Reply event.</param>
        //[Export("handleGetURLEvent:withReplyEvent:")]
        //private void HandleGetURLEvent(NSAppleEventDescriptor descriptor, NSAppleEventDescriptor replyEvent)
        //{
        //	try
        //	{
        //		string urlStr = descriptor.ParamDescriptorForKeyword(AEKeyword.DirectObject).StringValue;
        //		Console.WriteLine("HandleGetURLEvent={0}", urlStr);

        //		// 处理微信weixin://的协议
        //		ChatWebPage.Navigating(urlStr);
        //	}
        //	catch (Exception ex)
        //	{
        //		Console.WriteLine(ex.ToString());
        //	}
        //}

        /// <summary>
        /// 处理airdroid://协议的事件，需要info.plist配合
        /// </summary>
        /// <param name="obj">Object.</param>
        //[Export("handleUrlEvent:")]
        //private void handleUrlEvent(NSObject obj)
        //{

        //	if (obj != null)
        //		Console.WriteLine("handleUrlEvent: " + obj.ToString());
        //}

        //实现单击窗体的X按钮时，关闭窗体
        //public override bool ApplicationShouldTerminateAfterLastWindowClosed(NSApplication sender)
        //{
        //    ServiceMgr.LaunchctlUnload(ServiceMgr.ServiceNameForRS_Windows);
        //    return false;
        //}

        
        public override void WillTerminate(NSNotification notification)
        {
            try
            {
                ExitReason exitReason = DetectExitReason();
                Common.Log($"最终退出原因: {exitReason}", "AppDelegate");

                // 保存当前权限状态，为下次启动做准备
                SaveCurrentPermissionState();

                switch (exitReason)
                {
                    case ExitReason.UserExit:
                        // 用户主动退出：正常清理
                        Common.Log("执行用户退出清理", "AppDelegate");
                        PerformUserExitCleanup();
                        break;

                    case ExitReason.SystemPermissionRestart:
                        // 系统权限重启：几乎不做任何清理
                        Common.Log("检测到系统权限重启，跳过清理以确保可以重启", "AppDelegate");
                        PerformPermissionRestartCleanup();
                        break;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "WillTerminate");

                // 如果检测失败，默认为权限重启，不做任何清理
                Common.Log("检测失败，默认为权限重启模式，跳过清理", "AppDelegate");
                try
                {
                    SettingMgr.SetValue("LastExitWasPermissionRestart", DateTime.Now.Ticks.ToString());
                    Application.SetActivationPolicy(NSApplicationActivationPolicy.Regular);
                }
                catch (Exception ex2)
                {
                    Common.LogException(ex2, "紧急处理失败");
                }
            }
        }

        private ExitReason DetectExitReason()
        {
            try
            {
                Common.Log("=== DetectExitReason 开始检测 ===", "AppDelegate");

                // 0. 首先检查是否有用户主动退出标记
                bool userExitFlag = CheckUserExitFlag();
                Common.Log($"0. CheckUserExitFlag = {userExitFlag}", "AppDelegate");
                if (userExitFlag)
                {
                    Common.Log("Exit reason: 检测到用户主动退出标记", "AppDelegate");
                    return ExitReason.UserExit;
                }

                // 1. 检查启动时是否有权限请求标记（应用内权限按钮点击）
                bool hadPermissionRequest = CheckStartupPermissionRequest();
                Common.Log($"1. CheckStartupPermissionRequest = {hadPermissionRequest}", "AppDelegate");
                if (hadPermissionRequest)
                {
                    Common.Log("Exit reason: CheckStartupPermissionRequest = true", "AppDelegate");
                    return ExitReason.SystemPermissionRestart;
                }

                // 2. 检查权限状态变化（系统设置直接修改权限的情况）
                bool hasPermissionChange = HasRecentPermissionChange();
                Common.Log($"2. HasRecentPermissionChange = {hasPermissionChange}", "AppDelegate");

                // 3. 检查应用运行时间
                long startTimeTicks = SettingMgr.GetValue(APP_START_TIME_KEY, 0L);
                Common.Log($"3. StartTimeTicks = {startTimeTicks}", "AppDelegate");
                bool isShortRunTime = false;
                if (startTimeTicks > 0)
                {
                    DateTime startTime = new DateTime(startTimeTicks);
                    TimeSpan runTime = DateTime.Now - startTime;
                    Common.Log($"3. App running time = {runTime.TotalSeconds} seconds", "AppDelegate");
                    isShortRunTime = runTime.TotalSeconds < 30; // 恢复到30秒，给系统权限变化更多时间
                    Common.Log($"3. IsShortRunTime (< 30s) = {isShortRunTime}", "AppDelegate");
                }

                // 4. 如果有权限变化，进一步判断
                if (hasPermissionChange)
                {
                    // 4a. 如果运行时间很短，很可能是系统权限重启
                    if (isShortRunTime)
                    {
                        Common.Log("Exit reason: 权限状态变化 + 短时间运行 → 系统权限重启", "AppDelegate");
                        return ExitReason.SystemPermissionRestart;
                    }

                    // 4b. 即使运行时间较长，如果有明显的权限变化也可能是系统重启
                    bool hasSignificantPermissionChange = CheckSignificantPermissionChange();
                    Common.Log($"4b. CheckSignificantPermissionChange = {hasSignificantPermissionChange}", "AppDelegate");
                    if (hasSignificantPermissionChange)
                    {
                        Common.Log("Exit reason: 检测到重要权限变化 → 系统权限重启", "AppDelegate");
                        return ExitReason.SystemPermissionRestart;
                    }
                }

                // 5. 检查权限是否刚刚被授予（启动时权限状态检查）
                bool justGranted = CheckPermissionJustGranted();
                Common.Log($"5. CheckPermissionJustGranted = {justGranted}", "AppDelegate");
                if (justGranted)
                {
                    Common.Log("Exit reason: 权限刚被授予 → 系统权限重启", "AppDelegate");
                    return ExitReason.SystemPermissionRestart;
                }

                // 6. 如果运行时间很短但没有其他明确证据，需要谨慎判断
                if (isShortRunTime)
                {
                    Common.Log("Exit reason: 短时间运行但无明确权限变化证据，默认为用户退出", "AppDelegate");
                }

                Common.Log("Exit reason: UserExit (default)", "AppDelegate");
                return ExitReason.UserExit;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "DetectExitReason");
                return ExitReason.UserExit;
            }
        }

        // 实现Dock栏退出时触发
        //public override NSApplicationTerminateReply ApplicationShouldTerminate(NSApplication sender)
        //{
        //          ThreadMgr.AbortAll();
        //	TimerMgr.DisposeAll();

        //	return NSApplicationTerminateReply.Now;
        //}

        //      NSMenu dockMenu;

        //      public override NSMenu ApplicationDockMenu(NSApplication sender)
        //      {
        //          // 如果dockMenu为空，创建一个新的菜单
        //          if (dockMenu == null)
        //          {
        //              dockMenu = new NSMenu();

        //              // 创建菜单项
        //              var menuItem = new NSMenuItem("Say Hello", SayHello);
        //              dockMenu.AddItem(menuItem);
        //          }

        //          return dockMenu;
        //      }

        //      void SayHello(object sender, EventArgs e)
        //      {
        //          // 在这里处理菜单项点击事件
        //          var alert = new NSAlert
        //          {
        //              MessageText = "Hello from Dock Menu!",
        //              AlertStyle = NSAlertStyle.Informational
        //          };
        //          alert.RunModal();
        //      }


        public override bool ApplicationShouldHandleReopen(NSApplication sender, bool hasVisibleWindows)
        {
            if (this.MainWindow != null && !this.MainWindow.IsVisible)
            {
                //NSApplication.SharedApplication.doe
                this.MainWindow.OrderFront(null);
            }

            return false;
        }

        #region 权限检测方法

        /// <summary>
        /// 记录应用启动时的状态
        /// </summary>
        private void RecordStartupState()
        {
            try
            {
                Common.Log("=== 记录应用启动状态 ===", "AppDelegate");

                // 记录启动时间
                DateTime startTime = DateTime.Now;
                SettingMgr.SetValue(APP_START_TIME_KEY, startTime.Ticks);
                Common.Log($"启动时间: {startTime}", "AppDelegate");

                // 加载权限状态
                MyAPI.LoadAccesssPower();

                // 记录启动时的权限状态
                mStartupScreenPermission = MyAPI.AuthStatusForScreenCapture;
                mStartupAccessibilityPermission = MyAPI.AuthStatusForAccessibility;
                mStartupDiskPermission = MyAPI.AuthStatusForAllFiles;
                mStartupMicrophonePermission = MyAPI.AuthStatusForMicrophone;

                Common.Log($"启动时权限状态已记录: Screen={mStartupScreenPermission}, Accessibility={mStartupAccessibilityPermission}, Disk={mStartupDiskPermission}, Microphone={mStartupMicrophonePermission}", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "RecordStartupState");
            }
        }

        /// <summary>
        /// 检查是否有用户主动退出标记
        /// </summary>
        /// <returns></returns>
        private bool CheckUserExitFlag()
        {
            try
            {
                // 检查是否有用户主动退出的标记
                string userExitFlag = SettingMgr.GetValue("UserExitFlag", "");
                Common.Log($"用户退出标记: {userExitFlag}", "AppDelegate");

                if (!string.IsNullOrEmpty(userExitFlag))
                {
                    // 检查标记时间，如果在合理时间范围内则认为是用户主动退出
                    if (long.TryParse(userExitFlag, out long exitTime))
                    {
                        DateTime exitDateTime = new DateTime(exitTime);
                        TimeSpan timeSinceExit = DateTime.Now - exitDateTime;
                        Common.Log($"用户退出时间: {exitDateTime}, 距离现在: {timeSinceExit.TotalMinutes} 分钟", "AppDelegate");

                        if (timeSinceExit < TimeSpan.FromMinutes(5)) // 5分钟内的退出标记都认为有效
                        {
                            // 清除标记，避免下次误判
                            SettingMgr.SetValue("UserExitFlag", "");
                            Common.Log("检测到有效的用户退出标记，清除标记并返回true", "AppDelegate");
                            return true;
                        }
                        else
                        {
                            // 清除过期的标记
                            SettingMgr.SetValue("UserExitFlag", "");
                            Common.Log("用户退出标记已过期，清除标记", "AppDelegate");
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CheckUserExitFlag");
                return false;
            }
        }

        /// <summary>
        /// 检查启动时是否有权限请求标记
        /// </summary>
        /// <returns></returns>
        private bool CheckStartupPermissionRequest()
        {
            try
            {
                // 检查是否有权限请求相关的标记
                string permissionRequestFlag = SettingMgr.GetValue("PermissionRequesting", "");
                Common.Log($"权限请求标记: {permissionRequestFlag}", "AppDelegate");

                if (!string.IsNullOrEmpty(permissionRequestFlag))
                {
                    // 检查标记时间，如果在合理时间范围内则认为是权限重启
                    if (long.TryParse(permissionRequestFlag, out long requestTime))
                    {
                        DateTime requestDateTime = new DateTime(requestTime);
                        TimeSpan timeSinceRequest = DateTime.Now - requestDateTime;
                        Common.Log($"权限请求时间: {requestDateTime}, 距离现在: {timeSinceRequest.TotalMinutes} 分钟", "AppDelegate");

                        if (timeSinceRequest < TimeSpan.FromMinutes(10)) // 10分钟内的权限请求都认为有效
                        {
                            // 清除标记，避免下次误判
                            SettingMgr.SetValue("PermissionRequesting", "");
                            Common.Log("检测到有效的权限请求标记，清除标记并返回true", "AppDelegate");
                            return true;
                        }
                        else
                        {
                            // 清除过期的标记
                            SettingMgr.SetValue("PermissionRequesting", "");
                            Common.Log("权限请求标记已过期，清除标记", "AppDelegate");
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CheckStartupPermissionRequest");
                return false;
            }
        }

        /// <summary>
        /// 检查是否正在请求权限
        /// </summary>
        /// <returns></returns>
        private bool IsRequestingPermissions()
        {
            try
            {
                // 检查是否有权限请求相关的标记
                // 可以通过检查设置中的权限请求标记来判断
                string permissionRequestFlag = SettingMgr.GetValue("PermissionRequesting", "");
                if (!string.IsNullOrEmpty(permissionRequestFlag))
                {
                    // 检查标记时间，如果超过5分钟则清除标记
                    if (long.TryParse(permissionRequestFlag, out long requestTime))
                    {
                        DateTime requestDateTime = new DateTime(requestTime);
                        if (DateTime.Now - requestDateTime < TimeSpan.FromMinutes(5))
                        {
                            return true;
                        }
                        else
                        {
                            // 清除过期的标记
                            SettingMgr.SetValue("PermissionRequesting", "");
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "IsRequestingPermissions");
                return false;
            }
        }

        /// <summary>
        /// 检查最近是否有权限状态变化
        /// </summary>
        /// <returns></returns>
        private bool HasRecentPermissionChange()
        {
            try
            {
                // 获取上次保存的权限状态
                bool lastScreenPermission = SettingMgr.GetValue(PERM_SCREEN_KEY, false);
                bool lastAccessibilityPermission = SettingMgr.GetValue(PERM_ACCESSIBILITY_KEY, false);
                bool lastDiskPermission = SettingMgr.GetValue(PERM_DISK_KEY, false);
                bool lastMicrophonePermission = SettingMgr.GetValue(PERM_MICROPHONE_KEY, false);

                Common.Log($"上次权限状态: Screen={lastScreenPermission}, Accessibility={lastAccessibilityPermission}, Disk={lastDiskPermission}, Microphone={lastMicrophonePermission}", "AppDelegate");

                // 获取当前权限状态
                MyAPI.LoadAccesssPower();
                bool currentScreenPermission = MyAPI.AuthStatusForScreenCapture;
                bool currentAccessibilityPermission = MyAPI.AuthStatusForAccessibility;
                bool currentDiskPermission = MyAPI.AuthStatusForAllFiles;
                bool currentMicrophonePermission = MyAPI.AuthStatusForMicrophone;

                Common.Log($"当前权限状态: Screen={currentScreenPermission}, Accessibility={currentAccessibilityPermission}, Disk={currentDiskPermission}, Microphone={currentMicrophonePermission}", "AppDelegate");

                // 检查是否有权限状态变化
                bool screenChanged = lastScreenPermission != currentScreenPermission;
                bool accessibilityChanged = lastAccessibilityPermission != currentAccessibilityPermission;
                bool diskChanged = lastDiskPermission != currentDiskPermission;
                bool microphoneChanged = lastMicrophonePermission != currentMicrophonePermission;

                Common.Log($"权限变化: Screen={screenChanged}, Accessibility={accessibilityChanged}, Disk={diskChanged}, Microphone={microphoneChanged}", "AppDelegate");

                bool hasChange = screenChanged || accessibilityChanged || diskChanged || microphoneChanged;

                Common.Log($"HasRecentPermissionChange 结果: {hasChange}", "AppDelegate");
                return hasChange;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "HasRecentPermissionChange");
                return false;
            }
        }

        /// <summary>
        /// 保存当前权限状态（在确定退出原因后调用）
        /// </summary>
        private void SaveCurrentPermissionState()
        {
            try
            {
                MyAPI.LoadAccesssPower();
                bool currentScreenPermission = MyAPI.AuthStatusForScreenCapture;
                bool currentAccessibilityPermission = MyAPI.AuthStatusForAccessibility;
                bool currentDiskPermission = MyAPI.AuthStatusForAllFiles;
                bool currentMicrophonePermission = MyAPI.AuthStatusForMicrophone;

                // 保存当前权限状态
                SettingMgr.SetValue(PERM_SCREEN_KEY, currentScreenPermission);
                SettingMgr.SetValue(PERM_ACCESSIBILITY_KEY, currentAccessibilityPermission);
                SettingMgr.SetValue(PERM_DISK_KEY, currentDiskPermission);
                SettingMgr.SetValue(PERM_MICROPHONE_KEY, currentMicrophonePermission);

                Common.Log($"已保存当前权限状态: Screen={currentScreenPermission}, Accessibility={currentAccessibilityPermission}, Disk={currentDiskPermission}, Microphone={currentMicrophonePermission}", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "SaveCurrentPermissionState");
            }
        }

        /// <summary>
        /// 检查是否有重要的权限变化（用于系统设置直接修改的情况）
        /// </summary>
        /// <returns></returns>
        private bool CheckSignificantPermissionChange()
        {
            try
            {
                // 获取上次保存的权限状态
                bool lastScreenPermission = SettingMgr.GetValue(PERM_SCREEN_KEY, false);
                bool lastAccessibilityPermission = SettingMgr.GetValue(PERM_ACCESSIBILITY_KEY, false);
                bool lastDiskPermission = SettingMgr.GetValue(PERM_DISK_KEY, false);
                bool lastMicrophonePermission = SettingMgr.GetValue(PERM_MICROPHONE_KEY, false);

                // 获取当前权限状态
                MyAPI.LoadAccesssPower();
                bool currentScreenPermission = MyAPI.AuthStatusForScreenCapture;
                bool currentAccessibilityPermission = MyAPI.AuthStatusForAccessibility;
                bool currentDiskPermission = MyAPI.AuthStatusForAllFiles;
                bool currentMicrophonePermission = MyAPI.AuthStatusForMicrophone;

                Common.Log($"重要权限变化检测:", "AppDelegate");
                Common.Log($"  屏幕录制: {lastScreenPermission} → {currentScreenPermission}", "AppDelegate");
                Common.Log($"  辅助功能: {lastAccessibilityPermission} → {currentAccessibilityPermission}", "AppDelegate");
                Common.Log($"  完全磁盘访问: {lastDiskPermission} → {currentDiskPermission}", "AppDelegate");
                Common.Log($"  麦克风: {lastMicrophonePermission} → {currentMicrophonePermission}", "AppDelegate");

                // 检查关键权限的变化（特别是完全磁盘访问和屏幕录制）
                bool diskChanged = lastDiskPermission != currentDiskPermission;
                bool screenChanged = lastScreenPermission != currentScreenPermission;
                bool accessibilityChanged = lastAccessibilityPermission != currentAccessibilityPermission;

                // 这些权限的变化通常会导致应用重启
                bool hasSignificantChange = diskChanged || screenChanged || accessibilityChanged;

                Common.Log($"重要权限变化结果: 磁盘={diskChanged}, 屏幕={screenChanged}, 辅助功能={accessibilityChanged}, 总体={hasSignificantChange}", "AppDelegate");

                return hasSignificantChange;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CheckSignificantPermissionChange");
                return false;
            }
        }

        /// <summary>
        /// 检查权限是否刚刚被授予
        /// </summary>
        /// <returns></returns>
        private bool CheckPermissionJustGranted()
        {
            try
            {
                // 如果启动时的权限状态还没有记录，说明应用刚启动，不是权限变化导致的重启
                if (!mStartupScreenPermission.HasValue)
                {
                    Common.Log("启动时权限状态未记录，返回false", "AppDelegate");
                    return false;
                }

                Common.Log($"启动时权限状态: Screen={mStartupScreenPermission.Value}, Accessibility={mStartupAccessibilityPermission.Value}, Disk={mStartupDiskPermission.Value}, Microphone={mStartupMicrophonePermission.Value}", "AppDelegate");

                // 获取当前权限状态
                MyAPI.LoadAccesssPower();
                bool currentScreenPermission = MyAPI.AuthStatusForScreenCapture;
                bool currentAccessibilityPermission = MyAPI.AuthStatusForAccessibility;
                bool currentDiskPermission = MyAPI.AuthStatusForAllFiles;
                bool currentMicrophonePermission = MyAPI.AuthStatusForMicrophone;

                Common.Log($"当前权限状态: Screen={currentScreenPermission}, Accessibility={currentAccessibilityPermission}, Disk={currentDiskPermission}, Microphone={currentMicrophonePermission}", "AppDelegate");

                // 检查是否有权限从false变为true（即刚刚被授予）
                bool screenJustGranted = !mStartupScreenPermission.Value && currentScreenPermission;
                bool accessibilityJustGranted = !mStartupAccessibilityPermission.Value && currentAccessibilityPermission;
                bool diskJustGranted = !mStartupDiskPermission.Value && currentDiskPermission;
                bool microphoneJustGranted = !mStartupMicrophonePermission.Value && currentMicrophonePermission;

                Common.Log($"权限刚授予检测: Screen={screenJustGranted}, Accessibility={accessibilityJustGranted}, Disk={diskJustGranted}, Microphone={microphoneJustGranted}", "AppDelegate");

                bool result = screenJustGranted || accessibilityJustGranted || diskJustGranted || microphoneJustGranted;
                Common.Log($"CheckPermissionJustGranted 结果: {result}", "AppDelegate");

                return result;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CheckPermissionJustGranted");
                return false;
            }
        }

        /// <summary>
        /// 设置权限请求标记（供其他类调用）
        /// </summary>
        public static void SetPermissionRequestFlag()
        {
            try
            {
                SettingMgr.SetValue("PermissionRequesting", DateTime.Now.Ticks.ToString());
                Common.Log("Permission request flag set", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "SetPermissionRequestFlag");
            }
        }

        /// <summary>
        /// 清除权限请求标记
        /// </summary>
        public static void ClearPermissionRequestFlag()
        {
            try
            {
                SettingMgr.SetValue("PermissionRequesting", "");
                Common.Log("Permission request flag cleared", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ClearPermissionRequestFlag");
            }
        }

        /// <summary>
        /// 测试权限检测逻辑（用于调试）
        /// </summary>
        public static void TestPermissionDetection()
        {
            try
            {
                Common.Log("=== 开始测试权限检测逻辑 ===", "AppDelegate");

                // 模拟设置权限请求标记
                SetPermissionRequestFlag();

                // 创建一个临时的 AppDelegate 实例来测试检测逻辑
                var tempDelegate = new AppDelegate();

                // 测试各个检测方法
                bool startupRequest = tempDelegate.CheckStartupPermissionRequest();
                bool isRequesting = tempDelegate.IsRequestingPermissions();
                bool justGranted = tempDelegate.CheckPermissionJustGranted();
                bool hasChange = tempDelegate.HasRecentPermissionChange();

                Common.Log($"测试结果: StartupRequest={startupRequest}, IsRequesting={isRequesting}, JustGranted={justGranted}, HasChange={hasChange}", "AppDelegate");

                // 测试完整的检测逻辑
                ExitReason reason = tempDelegate.DetectExitReason();
                Common.Log($"最终检测结果: {reason}", "AppDelegate");

                Common.Log("=== 权限检测逻辑测试完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "TestPermissionDetection");
            }
        }

        /// <summary>
        /// 测试应用重启能力（用于调试）
        /// </summary>
        public static void TestAppRestartability()
        {
            try
            {
                Common.Log("=== 测试应用重启能力 ===", "AppDelegate");

                // 1. 检查当前应用状态
                CheckAppRestartability();

                // 2. 模拟权限重启清理
                Common.Log("--- 模拟权限重启清理 ---", "AppDelegate");
                var tempDelegate = new AppDelegate();
                tempDelegate.PerformPermissionRestartCleanup();

                // 3. 检查清理后的状态
                Common.Log("--- 检查清理后状态 ---", "AppDelegate");
                try
                {
                    // 使用 Application.SetActivationPolicy 确保应用可以重启
                    Application.SetActivationPolicy(NSApplicationActivationPolicy.Regular);
                    Common.Log("✅ 应用激活策略已设置为 Regular，应该可以重启", "AppDelegate");
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "设置激活策略失败");

                    // 备用方案
                    try
                    {
                        var app = NSApplication.SharedApplication;
                        if (app != null)
                        {
                            app.ActivateIgnoringOtherApps(true);
                            Common.Log("✅ 应用已激活（备用方案），应该可以重启", "AppDelegate");
                        }
                    }
                    catch (Exception ex2)
                    {
                        Common.LogException(ex2, "激活应用失败");
                        Common.Log("❌ 无法激活应用，可能影响重启", "AppDelegate");
                    }
                }

                Common.Log("=== 应用重启能力测试完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "TestAppRestartability");
            }
        }

        /// <summary>
        /// 手动触发权限检测（用于调试）
        /// </summary>
        public static void ManualPermissionTest()
        {
            try
            {
                Common.Log("=== 手动权限检测测试 ===", "AppDelegate");

                // 1. 测试应用内权限按钮点击
                Common.Log("--- 测试1: 应用内权限按钮点击 ---", "AppDelegate");
                ClearPermissionRequestFlag();
                iTong.CoreModule.PermissionHelper.ClearUserExitFlag();

                iTong.CoreModule.PermissionHelper.SetPermissionRequestFlag();
                System.Threading.Thread.Sleep(1000);

                var tempDelegate1 = new AppDelegate();
                ExitReason reason1 = tempDelegate1.DetectExitReason();

                Common.Log($"应用内权限按钮检测结果: {reason1}", "AppDelegate");
                if (reason1 == ExitReason.SystemPermissionRestart)
                {
                    Common.Log("✅ 应用内权限按钮测试成功！", "AppDelegate");
                }
                else
                {
                    Common.Log("❌ 应用内权限按钮测试失败！", "AppDelegate");
                }

                // 2. 测试系统设置直接修改权限
                Common.Log("--- 测试2: 系统设置直接修改权限 ---", "AppDelegate");
                ClearPermissionRequestFlag();
                iTong.CoreModule.PermissionHelper.ClearUserExitFlag();

                // 模拟权限状态变化（假设之前磁盘权限是false，现在是true）
                SettingMgr.SetValue("LastDiskPermission", false);
                System.Threading.Thread.Sleep(1000);

                var tempDelegate2 = new AppDelegate();
                ExitReason reason2 = tempDelegate2.DetectExitReason();

                Common.Log($"系统设置权限变化检测结果: {reason2}", "AppDelegate");
                if (reason2 == ExitReason.SystemPermissionRestart)
                {
                    Common.Log("✅ 系统设置权限变化测试成功！", "AppDelegate");
                }
                else
                {
                    Common.Log("❌ 系统设置权限变化测试失败！", "AppDelegate");
                }

                // 3. 测试用户主动退出
                Common.Log("--- 测试3: 用户主动退出 ---", "AppDelegate");
                ClearPermissionRequestFlag();
                iTong.CoreModule.PermissionHelper.SetUserExitFlag();

                System.Threading.Thread.Sleep(1000);

                var tempDelegate3 = new AppDelegate();
                ExitReason reason3 = tempDelegate3.DetectExitReason();

                Common.Log($"用户退出检测结果: {reason3}", "AppDelegate");
                if (reason3 == ExitReason.UserExit)
                {
                    Common.Log("✅ 用户退出测试成功！", "AppDelegate");
                }
                else
                {
                    Common.Log("❌ 用户退出测试失败！", "AppDelegate");
                }

                Common.Log("=== 手动权限检测测试完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ManualPermissionTest");
            }
        }

        #endregion

        #region 退出清理方法

        /// <summary>
        /// 执行用户主动退出的清理操作
        /// </summary>
        private void PerformUserExitCleanup()
        {
            try
            {
                Common.Log("开始用户退出清理...", "AppDelegate");

                // 检查 macOS 版本，决定清理策略
                var osVersion = Environment.OSVersion.Version;
                bool isProblematicVersion = (osVersion.Major == 10 && osVersion.Minor <= 13) ||
                                          (osVersion.Major == 11 && osVersion.Minor <= 7);

                if (isProblematicVersion)
                {
                    Common.Log("检测到有问题的 macOS 版本，使用兼容的退出策略", "AppDelegate");
                    PerformCompatibleUserExit();
                }
                else
                {
                    Common.Log("使用标准退出策略", "AppDelegate");
                    // 用户主动退出，执行完整的清理
                    ServiceMgr.ExitAppForRS();
                }

                Common.Log("用户退出清理完成", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "PerformUserExitCleanup");
            }
        }

        /// <summary>
        /// 执行兼容的用户退出（针对旧版 macOS）
        /// </summary>
        private void PerformCompatibleUserExit()
        {
            try
            {
                Common.Log("开始兼容的用户退出清理...", "AppDelegate");

                // 1. 设置用户退出标记
                try
                {
                    SettingMgr.SetValue("UserExitRequested", DateTime.Now.Ticks.ToString());
                    SettingMgr.SetValue("UserExitTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    Common.Log("已设置用户退出标记", "AppDelegate");
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "设置用户退出标记失败");
                }

                // 2. 隐藏状态栏图标
                try
                {
                    if (skStatusItem.Current != null)
                    {
                        skStatusItem.Current.Visible = false;
                        Common.Log("已隐藏状态栏图标", "AppDelegate");
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "隐藏状态栏图标失败");
                }

                // 3. 通知 Service 进程用户退出，但不强制杀死 Service
                try
                {
                    // 发送用户退出消息，让 Service 知道用户主动退出
                    SocketMgr.SendMsgFromClient(RSEventType.ExitApp, null, false);
                    Common.Log("已通知 Service 进程用户退出", "AppDelegate");
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "通知 Service 进程失败");
                }

                // 4. 短暂等待，让消息发送完成
                System.Threading.Thread.Sleep(500);

                // 5. 不调用 Application.SetActivationPolicy(NSApplicationActivationPolicy.Prohibited)
                // 6. 不强制杀死 Service 进程
                // 7. 让 GUI 进程自然退出，Service 进程继续运行但知道 GUI 已退出

                Common.Log("兼容的用户退出清理完成", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "PerformCompatibleUserExit");
            }
        }

        /// <summary>
        /// 执行权限重启的清理操作（针对 macOS 10.13.6/11.7.10 优化）
        /// </summary>
        private void PerformPermissionRestartCleanup()
        {
            try
            {
                Common.Log("开始权限重启清理（针对旧版 macOS 优化）...", "AppDelegate");

                // 1. 设置权限重启标记
                try
                {
                    SettingMgr.SetValue("LastExitWasPermissionRestart", DateTime.Now.Ticks.ToString());
                    SettingMgr.SetValue("PermissionRestartTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    SettingMgr.SetValue("NeedRestartGUI", "true");  // 新增：通知 Service 需要重启 GUI
                    Common.Log("已设置权限重启标记", "AppDelegate");
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "设置权限重启标记失败");
                }

                // 2. 确保应用保持可激活状态
                try
                {
                    Application.SetActivationPolicy(NSApplicationActivationPolicy.Regular);
                    Common.Log("已确保应用激活策略为 Regular", "AppDelegate");
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "设置激活策略失败");
                }

                // 3. 通知 Service 进程这是权限重启，请求重启 GUI
                try
                {
                    // 使用 ReStart 事件类型，这会让 Service 重启 GUI 进程
                    SocketMgr.SendMsgFromClient(RSEventType.ReStart, null, false);
                    Common.Log("已通知 Service 进程重启 GUI", "AppDelegate");

                    // 短暂等待，让重启消息发送完成
                    System.Threading.Thread.Sleep(200);
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "通知 Service 进程重启失败");

                    // 备用方案：使用传统的退出消息
                    try
                    {
                        SocketMgr.SendMsgFromClient(RSEventType.ExitApp, null, false);
                        Common.Log("已发送备用退出消息", "AppDelegate");
                    }
                    catch (Exception ex2)
                    {
                        Common.LogException(ex2, "发送备用消息也失败");
                    }
                }

                // 4. 短暂等待，让消息发送完成
                System.Threading.Thread.Sleep(300);

                // 5. 在旧版 macOS 上，我们需要更主动的重启策略
                try
                {
                    var osVersion = Environment.OSVersion.Version;
                    Common.Log($"当前 macOS 版本: {osVersion}", "AppDelegate");

                    // 检查是否是有问题的版本
                    bool isProblematicVersion = (osVersion.Major == 10 && osVersion.Minor <= 13) ||
                                              (osVersion.Major == 11 && osVersion.Minor <= 7);

                    if (isProblematicVersion)
                    {
                        Common.Log("检测到有问题的 macOS 版本，使用特殊重启策略", "AppDelegate");

                        // 设置延迟重启标记
                        SettingMgr.SetValue("DelayedRestartRequired", DateTime.Now.AddSeconds(3).Ticks.ToString());

                        // 请求 Service 进程在短时间后重启 GUI
                        ScheduleGUIRestart();
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "特殊重启策略失败");
                }

                Common.Log("权限重启清理完成（旧版 macOS 优化）", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "PerformPermissionRestartCleanup");
            }
        }

        /// <summary>
        /// 安排 GUI 重启（针对旧版 macOS）
        /// </summary>
        private void ScheduleGUIRestart()
        {
            try
            {
                Common.Log("安排 GUI 重启（通过 Service 进程）...", "AppDelegate");

                // 获取应用路径信息
                string appPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                string appDir = System.IO.Path.GetDirectoryName(appPath);

                Common.Log($"应用目录: {appDir}", "AppDelegate");

                // 保存重启信息，供 Service 进程使用
                SettingMgr.SetValue("RestartAppPath", $"{appDir}/Remote Support.app");
                SettingMgr.SetValue("RestartRequested", DateTime.Now.Ticks.ToString());

                // 主要依赖 RSEventType.ReStart 消息让 Service 处理重启
                // 这里只是保存备用信息

                Common.Log("已安排 GUI 重启（通过 Service）", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ScheduleGUIRestart");
            }
        }

        /// <summary>
        /// 检查应用启动环境，确保可以正常重启
        /// </summary>
        public static void CheckAppRestartability()
        {
            try
            {
                Common.Log("=== 检查应用重启能力 ===", "AppDelegate");

                // 1. 检查上次是否是权限重启
                string lastExitFlag = SettingMgr.GetValue("LastExitWasPermissionRestart", "");
                string lastExitTime = SettingMgr.GetValue("PermissionRestartTime", "");
                if (!string.IsNullOrEmpty(lastExitFlag))
                {
                    Common.Log($"检测到权限重启标记，重启时间: {lastExitTime}", "AppDelegate");

                    // 清除标记
                    SettingMgr.SetValue("LastExitWasPermissionRestart", "");
                    SettingMgr.SetValue("PermissionRestartTime", "");
                    Common.Log("已清除权限重启标记", "AppDelegate");

                    // 执行权限重启后的修复操作
                    PerformPostPermissionRestartFix();
                }

                // 2. 检查上次是否是用户退出
                string userExitFlag = SettingMgr.GetValue("UserExitRequested", "");
                string userExitTime = SettingMgr.GetValue("UserExitTime", "");
                if (!string.IsNullOrEmpty(userExitFlag))
                {
                    Common.Log($"检测到用户退出标记，退出时间: {userExitTime}", "AppDelegate");

                    // 清除标记
                    SettingMgr.SetValue("UserExitRequested", "");
                    SettingMgr.SetValue("UserExitTime", "");
                    Common.Log("已清除用户退出标记", "AppDelegate");

                    // 执行用户退出后的重新启动修复
                    PerformPostUserExitFix();
                }

                // 3. 检查应用激活策略
                try
                {
                    Application.SetActivationPolicy(NSApplicationActivationPolicy.Regular);
                    Common.Log("已确保应用激活策略为 Regular", "AppDelegate");
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "设置应用激活策略失败");
                }

                // 4. 检查和清理可能的问题状态
                try
                {
                    CleanupPotentialIssues();
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "清理潜在问题失败");
                }

                // 5. 检查进程状态
                try
                {
                    var currentProcess = System.Diagnostics.Process.GetCurrentProcess();
                    Common.Log($"当前进程ID: {currentProcess.Id}, 进程名: {currentProcess.ProcessName}", "AppDelegate");
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "获取进程信息失败");
                }

                Common.Log("=== 应用重启能力检查完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CheckAppRestartability");
            }
        }

        /// <summary>
        /// 执行权限重启后的修复操作
        /// </summary>
        private static void PerformPostPermissionRestartFix()
        {
            try
            {
                Common.Log("=== 执行权限重启后修复 ===", "AppDelegate");

                // 1. 清理重启相关标记
                try
                {
                    SettingMgr.SetValue("NeedRestartGUI", "");
                    SettingMgr.SetValue("DelayedRestartRequired", "");
                    SettingMgr.SetValue("RestartAppPath", "");
                    SettingMgr.SetValue("RestartRequested", "");
                    SettingMgr.SetValue("UserExitRequested", "");
                    SettingMgr.SetValue("UserExitTime", "");
                    Common.Log("已清理重启标记", "AppDelegate");
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "清理重启标记失败");
                }

                // 2. 重置应用状态
                try
                {
                    // 确保应用可以正常显示
                    var app = NSApplication.SharedApplication;
                    if (app != null)
                    {
                        app.ActivateIgnoringOtherApps(true);
                        Common.Log("已激活应用", "AppDelegate");
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "激活应用失败");
                }

                // 3. 检查是否需要通知 Service 进程 GUI 已重启
                try
                {
                    NotifyServiceGUIRestarted();
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "通知 Service 进程失败");
                }

                Common.Log("=== 权限重启后修复完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "PerformPostPermissionRestartFix");
            }
        }

        /// <summary>
        /// 通知 Service 进程 GUI 已重启
        /// </summary>
        private static void NotifyServiceGUIRestarted()
        {
            try
            {
                Common.Log("通知 Service 进程 GUI 已重启...", "AppDelegate");

                // 发送 GUI 重启完成的消息
                var guiRestartedEvent = new RSEvent()
                {
                    EventType = RSEventType.Connect,  // 使用连接事件表示 GUI 重新可用
                    Data = "GUI_RESTARTED_AFTER_PERMISSION"
                };

                SocketMgr.SendMsgFromClient(RSEventType.Connect, null, false);
                Common.Log("已通知 Service 进程 GUI 重启完成", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "NotifyServiceGUIRestarted");
            }
        }

        /// <summary>
        /// 执行用户退出后的重新启动修复
        /// </summary>
        private static void PerformPostUserExitFix()
        {
            try
            {
                Common.Log("=== 执行用户退出后重新启动修复 ===", "AppDelegate");

                // 1. 通知 Service 进程 GUI 重新启动了
                try
                {
                    SocketMgr.SendMsgFromClient(RSEventType.Connect, null, false);
                    Common.Log("已通知 Service 进程 GUI 重新连接", "AppDelegate");
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "通知 Service 进程失败");
                }

                // 2. 确保应用可以正常显示
                try
                {
                    var app = NSApplication.SharedApplication;
                    if (app != null)
                    {
                        app.ActivateIgnoringOtherApps(true);
                        Common.Log("已激活应用", "AppDelegate");
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "激活应用失败");
                }

                // 3. 重置状态栏图标（如果需要）
                try
                {
                    if (skStatusItem.Current != null)
                    {
                        skStatusItem.Current.Visible = true;
                        Common.Log("已显示状态栏图标", "AppDelegate");
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "显示状态栏图标失败");
                }

                Common.Log("=== 用户退出后重新启动修复完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "PerformPostUserExitFix");
            }
        }

        /// <summary>
        /// 清理可能导致应用无法启动的问题
        /// </summary>
        private static void CleanupPotentialIssues()
        {
            try
            {
                Common.Log("=== 清理潜在问题 ===", "AppDelegate");

                // 1. 检查并清理可能的进程残留
                try
                {
                    var currentProcess = System.Diagnostics.Process.GetCurrentProcess();
                    var processes = System.Diagnostics.Process.GetProcessesByName(currentProcess.ProcessName);
                    Common.Log($"找到 {processes.Length} 个同名进程", "AppDelegate");

                    foreach (var process in processes)
                    {
                        if (process.Id != currentProcess.Id)
                        {
                            Common.Log($"发现其他进程: PID={process.Id}", "AppDelegate");
                            // 不强制杀死，只记录
                        }
                        process.Dispose();
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "检查进程失败");
                }

                // 2. 重置一些可能有问题的设置
                try
                {
                    // 清理可能有问题的临时设置
                    SettingMgr.SetValue("AppStartupError", "");
                    SettingMgr.SetValue("LastCrashTime", "");
                    Common.Log("已清理临时设置", "AppDelegate");
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "清理设置失败");
                }

                Common.Log("=== 潜在问题清理完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CleanupPotentialIssues");
            }
        }

        /// <summary>
        /// 测试旧版 macOS 重启问题修复（用于调试）
        /// </summary>
        public static void TestOldMacOSRestartFix()
        {
            try
            {
                Common.Log("=== 测试旧版 macOS 重启问题修复 ===", "AppDelegate");

                var osVersion = Environment.OSVersion.Version;
                Common.Log($"当前 macOS 版本: {osVersion}", "AppDelegate");

                bool isProblematicVersion = (osVersion.Major == 10 && osVersion.Minor <= 13) ||
                                          (osVersion.Major == 11 && osVersion.Minor <= 7);

                if (isProblematicVersion)
                {
                    Common.Log("✅ 检测到有问题的 macOS 版本", "AppDelegate");
                }
                else
                {
                    Common.Log("ℹ️ 当前版本可能不需要特殊处理", "AppDelegate");
                }

                // 测试重启消息发送
                Common.Log("--- 测试重启消息发送 ---", "AppDelegate");
                try
                {
                    SocketMgr.SendMsgFromClient(RSEventType.ReStart, null, false);
                    Common.Log("✅ 重启消息发送成功", "AppDelegate");
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "❌ 重启消息发送失败");
                }

                Common.Log("=== 旧版 macOS 重启问题修复测试完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "TestOldMacOSRestartFix");
            }
        }

        /// <summary>
        /// 测试 macOS 11.7.10 重启问题修复（用于调试）
        /// </summary>
        public static void TestMacOS11RestartFix()
        {
            try
            {
                Common.Log("=== 测试 macOS 11.7.10 重启问题修复 ===", "AppDelegate");

                // 1. 模拟权限重启场景
                Common.Log("--- 模拟权限重启场景 ---", "AppDelegate");

                // 清除所有标记
                ClearPermissionRequestFlag();
                iTong.CoreModule.PermissionHelper.ClearUserExitFlag();
                SettingMgr.SetValue("LastExitWasPermissionRestart", "");

                // 设置权限请求标记
                iTong.CoreModule.PermissionHelper.SetPermissionRequestFlag();

                // 模拟检测
                var tempDelegate = new AppDelegate();
                ExitReason reason = tempDelegate.DetectExitReason();
                Common.Log($"检测结果: {reason}", "AppDelegate");

                if (reason == ExitReason.SystemPermissionRestart)
                {
                    Common.Log("✅ 正确检测为权限重启", "AppDelegate");

                    // 模拟权限重启清理
                    tempDelegate.PerformPermissionRestartCleanup();

                    // 检查清理后的状态
                    try
                    {
                        Application.SetActivationPolicy(NSApplicationActivationPolicy.Regular);
                        Common.Log("✅ 应用激活策略正常", "AppDelegate");
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex, "❌ 应用激活策略设置失败");
                    }

                    // 检查是否设置了重启标记
                    string restartFlag = SettingMgr.GetValue("LastExitWasPermissionRestart", "");
                    if (!string.IsNullOrEmpty(restartFlag))
                    {
                        Common.Log("✅ 权限重启标记已设置", "AppDelegate");
                    }
                    else
                    {
                        Common.Log("❌ 权限重启标记未设置", "AppDelegate");
                    }
                }
                else
                {
                    Common.Log("❌ 检测结果不正确", "AppDelegate");
                }

                // 2. 测试启动时修复
                Common.Log("--- 测试启动时修复 ---", "AppDelegate");
                CheckAppRestartability();

                Common.Log("=== macOS 11.7.10 重启问题修复测试完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "TestMacOS11RestartFix");
            }
        }

        /// <summary>
        /// 测试用户退出和重启修复（用于调试）
        /// </summary>
        public static void TestUserExitAndRestart()
        {
            try
            {
                Common.Log("=== 测试用户退出和重启修复 ===", "AppDelegate");

                var osVersion = Environment.OSVersion.Version;
                Common.Log($"当前 macOS 版本: {osVersion}", "AppDelegate");

                bool isProblematicVersion = (osVersion.Major == 10 && osVersion.Minor <= 13) ||
                                          (osVersion.Major == 11 && osVersion.Minor <= 7);

                if (isProblematicVersion)
                {
                    Common.Log("✅ 检测到有问题的 macOS 版本，将使用兼容退出策略", "AppDelegate");

                    // 模拟兼容的用户退出
                    Common.Log("--- 模拟兼容的用户退出 ---", "AppDelegate");
                    var tempDelegate = new AppDelegate();
                    tempDelegate.PerformCompatibleUserExit();

                    // 检查退出标记是否设置
                    string userExitFlag = SettingMgr.GetValue("UserExitRequested", "");
                    if (!string.IsNullOrEmpty(userExitFlag))
                    {
                        Common.Log("✅ 用户退出标记已设置", "AppDelegate");
                    }
                    else
                    {
                        Common.Log("❌ 用户退出标记未设置", "AppDelegate");
                    }

                    // 模拟重新启动后的修复
                    Common.Log("--- 模拟重新启动后的修复 ---", "AppDelegate");
                    PerformPostUserExitFix();

                    // 检查标记是否被清理
                    string userExitFlagAfter = SettingMgr.GetValue("UserExitRequested", "");
                    if (string.IsNullOrEmpty(userExitFlagAfter))
                    {
                        Common.Log("✅ 用户退出标记已清理", "AppDelegate");
                    }
                    else
                    {
                        Common.Log("❌ 用户退出标记未清理", "AppDelegate");
                    }
                }
                else
                {
                    Common.Log("ℹ️ 当前版本使用标准退出策略", "AppDelegate");
                }

                Common.Log("=== 用户退出和重启修复测试完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "TestUserExitAndRestart");
            }
        }

        #endregion
    }
}
