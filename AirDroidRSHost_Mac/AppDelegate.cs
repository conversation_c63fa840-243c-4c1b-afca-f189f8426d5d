using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel;
using System.IO;
using System.Diagnostics;

using AppKit;
using Foundation;
using ObjCRuntime;
using CoreGraphics;


using iTong.Android;
using iTong.CoreFoundation;
using iTong.CoreModule;

namespace AirDroidRemoteSupportHost
{
	/// <summary>
	/// 应用退出原因枚举
	/// </summary>
	public enum ExitReason
	{
		/// <summary>用户主动退出</summary>
		UserExit,
		/// <summary>系统权限重启</summary>
		SystemPermissionRestart
	}

	//public static class AEKeyword
	//{
	//	public static readonly uint DirectObject = Create("----");
	//	public static readonly uint ErrorNumber = Create("errn");
	//	public static readonly uint ErrorString = Create("errs");
	//	public static readonly uint ProcessSerialNumber = Create("psn ");
	//	public static readonly uint PreDispatch = Create("phac");
	//	public static readonly uint SelectProc = Create("selh");
	//	public static readonly uint AERecorderCount = Create("recr");
	//	public static readonly uint AEVersion = Create("vers");

	//	private static uint Create(string key)
	//	{
	//		return (((uint)key[0]) << 24 |
	//				((uint)key[1]) << 16 |
	//				((uint)key[2]) << 8 |
	//				((uint)key[3]) << 0);
	//	}
	//}

	public partial class AppDelegate : NSApplicationDelegate
	{

        protected skWindow MainWindow { get; set; }

        #region 权限状态存储

        /// <summary>应用启动时的权限状态</summary>
        private static bool? mStartupScreenPermission = null;
        private static bool? mStartupAccessibilityPermission = null;
        private static bool? mStartupDiskPermission = null;
        private static bool? mStartupMicrophonePermission = null;

        /// <summary>上次保存的权限状态</summary>
        private const string PERM_SCREEN_KEY = "LastScreenPermission";
        private const string PERM_ACCESSIBILITY_KEY = "LastAccessibilityPermission";
        private const string PERM_DISK_KEY = "LastDiskPermission";
        private const string PERM_MICROPHONE_KEY = "LastMicrophonePermission";
        private const string APP_START_TIME_KEY = "AppStartTime";

        #endregion

		public static AppDelegate Instance
		{
			get
			{
				return NSApplication.SharedApplication.Delegate as AppDelegate;
			}
		}

		public void ExitApp()
		{
			NSApplication.SharedApplication.Terminate(this);
		}

		public override void DidFinishLaunching (NSNotification notification)
		{
            //通过WebView的策略控制来实现URL跳转打开

            // 记录应用启动时间和权限状态
            RecordStartupState();

            string[] arrPara = ParaMgr.Arguments;
            if (arrPara == null || arrPara.Length == 0)
                return;

            System.Windows.Forms.ExtendControl.IsWindowAnchor = true;

            switch (arrPara[0])
            {
                case ParaMgr.CommandRunWidnows:
                    {
                        rsMainForm view = new rsMainForm();
                        view.Show();

                        //保存MainWindow
                        this.MainWindow = (skWindow)view.Window;

                        break;
                    }

                case ParaMgr.CommandRunSafeMode:
                    {
                        frmSafeMode.Init();
                        break;
                    }
            }
        }

        /// <summary>
        /// 协议：weixin://, tongbu:// 避免WebKit弹出窗体(需要设置info.plist中的URL types的URL Schemes
        /// </summary>
        /// <param name="descriptor">Descriptor.</param>
        /// <param name="replyEvent">Reply event.</param>
        //[Export("handleGetURLEvent:withReplyEvent:")]
        //private void HandleGetURLEvent(NSAppleEventDescriptor descriptor, NSAppleEventDescriptor replyEvent)
        //{
        //	try
        //	{
        //		string urlStr = descriptor.ParamDescriptorForKeyword(AEKeyword.DirectObject).StringValue;
        //		Console.WriteLine("HandleGetURLEvent={0}", urlStr);

        //		// 处理微信weixin://的协议
        //		ChatWebPage.Navigating(urlStr);
        //	}
        //	catch (Exception ex)
        //	{
        //		Console.WriteLine(ex.ToString());
        //	}
        //}

        /// <summary>
        /// 处理airdroid://协议的事件，需要info.plist配合
        /// </summary>
        /// <param name="obj">Object.</param>
        //[Export("handleUrlEvent:")]
        //private void handleUrlEvent(NSObject obj)
        //{

        //	if (obj != null)
        //		Console.WriteLine("handleUrlEvent: " + obj.ToString());
        //}

        //实现单击窗体的X按钮时，关闭窗体
        //public override bool ApplicationShouldTerminateAfterLastWindowClosed(NSApplication sender)
        //{
        //    ServiceMgr.LaunchctlUnload(ServiceMgr.ServiceNameForRS_Windows);
        //    return false;
        //}

        
        public override void WillTerminate(NSNotification notification)
        {
            //ServiceMgr.ExitAppForRS();

            ExitReason exitReason = DetectExitReason();
            Common.Log($"最终退出原因: {exitReason}", "AppDelegate");
            switch (exitReason)
            {
                case ExitReason.UserExit:
                    // 用户主动退出：正常清理
                    Common.Log("执行用户退出清理", "AppDelegate");
                    ServiceMgr.ExitAppForRS();
                    break;

                case ExitReason.SystemPermissionRestart:
                    // 系统权限重启：快速清理
                    Common.Log("执行系统权限重启清理", "AppDelegate");
                    //ServiceMgr.ExitAppForRS(false);
                    break;
            }
        }

        private ExitReason DetectExitReason()
        {
            try
            {
                Common.Log("=== DetectExitReason 开始检测 ===", "AppDelegate");

                // 0. 检查启动时是否有权限请求标记（最可靠的方法）
                bool hadPermissionRequest = CheckStartupPermissionRequest();
                Common.Log($"0. CheckStartupPermissionRequest = {hadPermissionRequest}", "AppDelegate");
                if (hadPermissionRequest)
                {
                    Common.Log("Exit reason: CheckStartupPermissionRequest = true", "AppDelegate");
                    return ExitReason.SystemPermissionRestart;
                }

                // 1. 检查是否正在请求权限
                bool isRequesting = IsRequestingPermissions();
                Common.Log($"1. IsRequestingPermissions = {isRequesting}", "AppDelegate");
                if (isRequesting)
                {
                    Common.Log("Exit reason: IsRequestingPermissions = true", "AppDelegate");
                    return ExitReason.SystemPermissionRestart;
                }

                // 2. 检查权限是否刚刚被授予
                bool justGranted = CheckPermissionJustGranted();
                Common.Log($"2. CheckPermissionJustGranted = {justGranted}", "AppDelegate");
                if (justGranted)
                {
                    Common.Log("Exit reason: CheckPermissionJustGranted = true", "AppDelegate");
                    return ExitReason.SystemPermissionRestart;
                }

                // 3. 检查最近是否有权限状态变化
                bool hasChange = HasRecentPermissionChange();
                Common.Log($"3. HasRecentPermissionChange = {hasChange}", "AppDelegate");
                if (hasChange)
                {
                    Common.Log("Exit reason: HasRecentPermissionChange = true", "AppDelegate");
                    return ExitReason.SystemPermissionRestart;
                }

                // 4. 检查应用运行时间（权限重启通常很快）
                long startTimeTicks = SettingMgr.GetValue(APP_START_TIME_KEY, 0L);
                Common.Log($"4. StartTimeTicks = {startTimeTicks}", "AppDelegate");
                if (startTimeTicks > 0)
                {
                    DateTime startTime = new DateTime(startTimeTicks);
                    TimeSpan runTime = DateTime.Now - startTime;
                    Common.Log($"4. App running time = {runTime.TotalSeconds} seconds", "AppDelegate");
                    if (runTime.TotalSeconds < 30)
                    {
                        Common.Log($"Exit reason: App running time = {runTime.TotalSeconds} seconds (< 30)", "AppDelegate");
                        return ExitReason.SystemPermissionRestart;
                    }
                }

                Common.Log("Exit reason: UserExit (default)", "AppDelegate");
                return ExitReason.UserExit;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "DetectExitReason");
                return ExitReason.UserExit;
            }
        }

        // 实现Dock栏退出时触发
        //public override NSApplicationTerminateReply ApplicationShouldTerminate(NSApplication sender)
        //{
        //          ThreadMgr.AbortAll();
        //	TimerMgr.DisposeAll();

        //	return NSApplicationTerminateReply.Now;
        //}

        //      NSMenu dockMenu;

        //      public override NSMenu ApplicationDockMenu(NSApplication sender)
        //      {
        //          // 如果dockMenu为空，创建一个新的菜单
        //          if (dockMenu == null)
        //          {
        //              dockMenu = new NSMenu();

        //              // 创建菜单项
        //              var menuItem = new NSMenuItem("Say Hello", SayHello);
        //              dockMenu.AddItem(menuItem);
        //          }

        //          return dockMenu;
        //      }

        //      void SayHello(object sender, EventArgs e)
        //      {
        //          // 在这里处理菜单项点击事件
        //          var alert = new NSAlert
        //          {
        //              MessageText = "Hello from Dock Menu!",
        //              AlertStyle = NSAlertStyle.Informational
        //          };
        //          alert.RunModal();
        //      }


        public override bool ApplicationShouldHandleReopen(NSApplication sender, bool hasVisibleWindows)
        {
            if (this.MainWindow != null && !this.MainWindow.IsVisible)
            {
                //NSApplication.SharedApplication.doe
                this.MainWindow.OrderFront(null);
            }

            return false;
        }

        #region 权限检测方法

        /// <summary>
        /// 记录应用启动时的状态
        /// </summary>
        private void RecordStartupState()
        {
            try
            {
                Common.Log("=== 记录应用启动状态 ===", "AppDelegate");

                // 记录启动时间
                DateTime startTime = DateTime.Now;
                SettingMgr.SetValue(APP_START_TIME_KEY, startTime.Ticks);
                Common.Log($"启动时间: {startTime}", "AppDelegate");

                // 加载权限状态
                MyAPI.LoadAccesssPower();

                // 记录启动时的权限状态
                mStartupScreenPermission = MyAPI.AuthStatusForScreenCapture;
                mStartupAccessibilityPermission = MyAPI.AuthStatusForAccessibility;
                mStartupDiskPermission = MyAPI.AuthStatusForAllFiles;
                mStartupMicrophonePermission = MyAPI.AuthStatusForMicrophone;

                Common.Log($"启动时权限状态已记录: Screen={mStartupScreenPermission}, Accessibility={mStartupAccessibilityPermission}, Disk={mStartupDiskPermission}, Microphone={mStartupMicrophonePermission}", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "RecordStartupState");
            }
        }

        /// <summary>
        /// 检查启动时是否有权限请求标记
        /// </summary>
        /// <returns></returns>
        private bool CheckStartupPermissionRequest()
        {
            try
            {
                // 检查是否有权限请求相关的标记
                string permissionRequestFlag = SettingMgr.GetValue("PermissionRequesting", "");
                Common.Log($"权限请求标记: {permissionRequestFlag}", "AppDelegate");

                if (!string.IsNullOrEmpty(permissionRequestFlag))
                {
                    // 检查标记时间，如果在合理时间范围内则认为是权限重启
                    if (long.TryParse(permissionRequestFlag, out long requestTime))
                    {
                        DateTime requestDateTime = new DateTime(requestTime);
                        TimeSpan timeSinceRequest = DateTime.Now - requestDateTime;
                        Common.Log($"权限请求时间: {requestDateTime}, 距离现在: {timeSinceRequest.TotalMinutes} 分钟", "AppDelegate");

                        if (timeSinceRequest < TimeSpan.FromMinutes(10)) // 10分钟内的权限请求都认为有效
                        {
                            // 清除标记，避免下次误判
                            SettingMgr.SetValue("PermissionRequesting", "");
                            Common.Log("检测到有效的权限请求标记，清除标记并返回true", "AppDelegate");
                            return true;
                        }
                        else
                        {
                            // 清除过期的标记
                            SettingMgr.SetValue("PermissionRequesting", "");
                            Common.Log("权限请求标记已过期，清除标记", "AppDelegate");
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CheckStartupPermissionRequest");
                return false;
            }
        }

        /// <summary>
        /// 检查是否正在请求权限
        /// </summary>
        /// <returns></returns>
        private bool IsRequestingPermissions()
        {
            try
            {
                // 检查是否有权限请求相关的标记
                // 可以通过检查设置中的权限请求标记来判断
                string permissionRequestFlag = SettingMgr.GetValue("PermissionRequesting", "");
                if (!string.IsNullOrEmpty(permissionRequestFlag))
                {
                    // 检查标记时间，如果超过5分钟则清除标记
                    if (long.TryParse(permissionRequestFlag, out long requestTime))
                    {
                        DateTime requestDateTime = new DateTime(requestTime);
                        if (DateTime.Now - requestDateTime < TimeSpan.FromMinutes(5))
                        {
                            return true;
                        }
                        else
                        {
                            // 清除过期的标记
                            SettingMgr.SetValue("PermissionRequesting", "");
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "IsRequestingPermissions");
                return false;
            }
        }

        /// <summary>
        /// 检查最近是否有权限状态变化
        /// </summary>
        /// <returns></returns>
        private bool HasRecentPermissionChange()
        {
            try
            {
                // 获取上次保存的权限状态
                bool lastScreenPermission = SettingMgr.GetValue(PERM_SCREEN_KEY, false);
                bool lastAccessibilityPermission = SettingMgr.GetValue(PERM_ACCESSIBILITY_KEY, false);
                bool lastDiskPermission = SettingMgr.GetValue(PERM_DISK_KEY, false);
                bool lastMicrophonePermission = SettingMgr.GetValue(PERM_MICROPHONE_KEY, false);

                Common.Log($"上次权限状态: Screen={lastScreenPermission}, Accessibility={lastAccessibilityPermission}, Disk={lastDiskPermission}, Microphone={lastMicrophonePermission}", "AppDelegate");

                // 获取当前权限状态
                MyAPI.LoadAccesssPower();
                bool currentScreenPermission = MyAPI.AuthStatusForScreenCapture;
                bool currentAccessibilityPermission = MyAPI.AuthStatusForAccessibility;
                bool currentDiskPermission = MyAPI.AuthStatusForAllFiles;
                bool currentMicrophonePermission = MyAPI.AuthStatusForMicrophone;

                Common.Log($"当前权限状态: Screen={currentScreenPermission}, Accessibility={currentAccessibilityPermission}, Disk={currentDiskPermission}, Microphone={currentMicrophonePermission}", "AppDelegate");

                // 检查是否有权限状态变化
                bool screenChanged = lastScreenPermission != currentScreenPermission;
                bool accessibilityChanged = lastAccessibilityPermission != currentAccessibilityPermission;
                bool diskChanged = lastDiskPermission != currentDiskPermission;
                bool microphoneChanged = lastMicrophonePermission != currentMicrophonePermission;

                Common.Log($"权限变化: Screen={screenChanged}, Accessibility={accessibilityChanged}, Disk={diskChanged}, Microphone={microphoneChanged}", "AppDelegate");

                bool hasChange = screenChanged || accessibilityChanged || diskChanged || microphoneChanged;

                // 保存当前权限状态
                SettingMgr.SetValue(PERM_SCREEN_KEY, currentScreenPermission);
                SettingMgr.SetValue(PERM_ACCESSIBILITY_KEY, currentAccessibilityPermission);
                SettingMgr.SetValue(PERM_DISK_KEY, currentDiskPermission);
                SettingMgr.SetValue(PERM_MICROPHONE_KEY, currentMicrophonePermission);

                Common.Log($"HasRecentPermissionChange 结果: {hasChange}", "AppDelegate");
                return hasChange;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "HasRecentPermissionChange");
                return false;
            }
        }

        /// <summary>
        /// 检查权限是否刚刚被授予
        /// </summary>
        /// <returns></returns>
        private bool CheckPermissionJustGranted()
        {
            try
            {
                // 如果启动时的权限状态还没有记录，说明应用刚启动，不是权限变化导致的重启
                if (!mStartupScreenPermission.HasValue)
                {
                    Common.Log("启动时权限状态未记录，返回false", "AppDelegate");
                    return false;
                }

                Common.Log($"启动时权限状态: Screen={mStartupScreenPermission.Value}, Accessibility={mStartupAccessibilityPermission.Value}, Disk={mStartupDiskPermission.Value}, Microphone={mStartupMicrophonePermission.Value}", "AppDelegate");

                // 获取当前权限状态
                MyAPI.LoadAccesssPower();
                bool currentScreenPermission = MyAPI.AuthStatusForScreenCapture;
                bool currentAccessibilityPermission = MyAPI.AuthStatusForAccessibility;
                bool currentDiskPermission = MyAPI.AuthStatusForAllFiles;
                bool currentMicrophonePermission = MyAPI.AuthStatusForMicrophone;

                Common.Log($"当前权限状态: Screen={currentScreenPermission}, Accessibility={currentAccessibilityPermission}, Disk={currentDiskPermission}, Microphone={currentMicrophonePermission}", "AppDelegate");

                // 检查是否有权限从false变为true（即刚刚被授予）
                bool screenJustGranted = !mStartupScreenPermission.Value && currentScreenPermission;
                bool accessibilityJustGranted = !mStartupAccessibilityPermission.Value && currentAccessibilityPermission;
                bool diskJustGranted = !mStartupDiskPermission.Value && currentDiskPermission;
                bool microphoneJustGranted = !mStartupMicrophonePermission.Value && currentMicrophonePermission;

                Common.Log($"权限刚授予检测: Screen={screenJustGranted}, Accessibility={accessibilityJustGranted}, Disk={diskJustGranted}, Microphone={microphoneJustGranted}", "AppDelegate");

                bool result = screenJustGranted || accessibilityJustGranted || diskJustGranted || microphoneJustGranted;
                Common.Log($"CheckPermissionJustGranted 结果: {result}", "AppDelegate");

                return result;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CheckPermissionJustGranted");
                return false;
            }
        }

        /// <summary>
        /// 设置权限请求标记（供其他类调用）
        /// </summary>
        public static void SetPermissionRequestFlag()
        {
            try
            {
                SettingMgr.SetValue("PermissionRequesting", DateTime.Now.Ticks.ToString());
                Common.Log("Permission request flag set", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "SetPermissionRequestFlag");
            }
        }

        /// <summary>
        /// 清除权限请求标记
        /// </summary>
        public static void ClearPermissionRequestFlag()
        {
            try
            {
                SettingMgr.SetValue("PermissionRequesting", "");
                Common.Log("Permission request flag cleared", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ClearPermissionRequestFlag");
            }
        }

        /// <summary>
        /// 测试权限检测逻辑（用于调试）
        /// </summary>
        public static void TestPermissionDetection()
        {
            try
            {
                Common.Log("=== 开始测试权限检测逻辑 ===", "AppDelegate");

                // 模拟设置权限请求标记
                SetPermissionRequestFlag();

                // 创建一个临时的 AppDelegate 实例来测试检测逻辑
                var tempDelegate = new AppDelegate();

                // 测试各个检测方法
                bool startupRequest = tempDelegate.CheckStartupPermissionRequest();
                bool isRequesting = tempDelegate.IsRequestingPermissions();
                bool justGranted = tempDelegate.CheckPermissionJustGranted();
                bool hasChange = tempDelegate.HasRecentPermissionChange();

                Common.Log($"测试结果: StartupRequest={startupRequest}, IsRequesting={isRequesting}, JustGranted={justGranted}, HasChange={hasChange}", "AppDelegate");

                // 测试完整的检测逻辑
                ExitReason reason = tempDelegate.DetectExitReason();
                Common.Log($"最终检测结果: {reason}", "AppDelegate");

                Common.Log("=== 权限检测逻辑测试完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "TestPermissionDetection");
            }
        }

        /// <summary>
        /// 手动触发权限检测（用于调试）
        /// </summary>
        public static void ManualPermissionTest()
        {
            try
            {
                Common.Log("=== 手动权限检测测试 ===", "AppDelegate");

                // 1. 先清除所有标记
                ClearPermissionRequestFlag();

                // 2. 模拟用户点击权限按钮
                Common.Log("模拟用户点击权限按钮...", "AppDelegate");
                iTong.CoreModule.PermissionHelper.SetPermissionRequestFlag();

                // 3. 等待一秒
                System.Threading.Thread.Sleep(1000);

                // 4. 模拟应用重启后的检测
                Common.Log("模拟应用重启后的检测...", "AppDelegate");
                var tempDelegate = new AppDelegate();
                ExitReason reason = tempDelegate.DetectExitReason();

                Common.Log($"检测结果: {reason}", "AppDelegate");

                if (reason == ExitReason.SystemPermissionRestart)
                {
                    Common.Log("✅ 测试成功！正确检测到权限重启", "AppDelegate");
                }
                else
                {
                    Common.Log("❌ 测试失败！未能检测到权限重启", "AppDelegate");
                }

                Common.Log("=== 手动权限检测测试完成 ===", "AppDelegate");
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ManualPermissionTest");
            }
        }

        #endregion
    }
}
