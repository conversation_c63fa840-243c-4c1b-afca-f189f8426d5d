﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

using iTong.Android;
using iTong.CoreFoundation;

#if MAC
using ToolStripSeparator = AppKit.NSMenuItem;
using ToolStripMenuItem = AppKit.NSMenuItem;
using ToolStripItem = AppKit.NSMenuItem;
using ContextMenuStrip = AppKit.NSMenu;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Image = AppKit.NSImage;
using Icon = AppKit.NSImage;
using NSScreen = AppKit.NSScreen;
using Rectangle = CoreGraphics.CGRect;
using Form = AppKit.NSWindow;
using Label = AppKit.NSTextView;

using AppKit;
using CoreGraphics;
using Foundation;
#endif

namespace iTong.CoreModule
{
    public class TestArg : EventArgs
    {
        public string Title = string.Empty;
        public bool Visible = false;
        public Color TitleColor = Color.Red;
        public bool NeedLoadUrl = false;

        public MenuType Type = MenuType.None;
    }

    public enum MenuType
    {
        None,

        tsmiChangeMode,
        tsmiOpenLogFolder,
        tsmiClearLog,
        tsmiCreateLog,
        tsmiUpLogToJira,
        tsmiWebRtcSate,
        tsmiClearAll,

        tsmiToNormal,
        tsmiToNormalLog,
        tsmiToTest,
        tsmiToTestLog,

        MenuOpening,
        MenuClosed,
    }

    public static class MyTest
    {
        private static Control lblInTestMode;

        private static ToolStripSeparator tssSplit;
        private static ToolStripMenuItem tsmiChangeMode;
        private static ToolStripMenuItem tsmiOpenLogFolder;
        private static ToolStripMenuItem tsmiClearLog;       
        private static ToolStripMenuItem tsmiCreateLog;
        private static ToolStripMenuItem tsmiUpLogToJira;
        private static ToolStripMenuItem tsmiOpenFpsData;
        private static ToolStripMenuItem tsmiClearAll;

        private static ToolStripMenuItem tsmiToNormal;
        private static ToolStripMenuItem tsmiToNormalLog;
        private static ToolStripMenuItem tsmiToTest;
        private static ToolStripMenuItem tsmiToTestLog;

        private static string ToNormal = "切换到正式环境";
        private static string ToNormalLog = "切换到正式环境日志模式";
        private static string ToTest = "切换到测试环境";
        private static string ToTestLog = "切换到测试环境日志模式";                  
        private static string ChangeMode = "切换模式";
        private static string OpenLogFolder = "打开日志目录";
        private static string ClearLog = "清空日志文件";
        private static string CreateLog = "打包日志文件";
        private static string ClearAll = "恢复默认设置";
        private static string UpLogToJira = "上传日志到Jira";
        private static string Warning = "警告";
        private static string DeleteMsg = "你确定要清空下面目录的所有文件吗？";
        private static string OK = "确定";
        private static string Cancel = "取消";
        private static string UploadJiraTitle = "上传日志";
        private static string UploadJiraMsg = "请输入JIRA任务的完整Id，用于上传日志到JIRA上";
        private static string UploadLogToJiraSuccessfully = "上传日志到JIRA成功";
        private static string UploadLogToJiraFailed = "上传日志到JIRA失败";
        private static string CompressingLogFiles = "正在压缩日志文件";

        private static int DoubleClickCount { get ; set; } = 0;
        private static bool? LastInTestMode;
        private static bool FirstShowMenu { get; set; } = false;

        public static event EventHandler<TestArg> Callback;


        public static List<ToolStripItem> ListMenuItems = new List<ToolStripItem>();

        public static void SetDoubleClick()
        {
            DoubleClickCount += 1;
            SetCurrentMode();
        }

        private static void SetCurrentMode()
        {
            MenuType type = MenuType.None;

            LastInTestMode = MyLog.IsTestMode;

            switch (DoubleClickCount)
            {
                case 3: // 3:log mode
                    MyLog.ShowLog = true;
                    MyLog.IsTestMode = false;
                    type = MenuType.tsmiToNormalLog;
                    break;

                case 4: // 4:test and log mode
                    MyLog.ShowLog = true;
                    MyLog.IsTestMode = true;
                    type = MenuType.tsmiToTest;
                    break;

                case 5: // 5:test mode
                    MyLog.IsTestMode = true;
                    MyLog.ShowLog = false;
                    type = MenuType.tsmiToTestLog;
                    break;

                default: // 0 1 2 6:return normal mode and this count reset to 0
                    MyLog.ShowLog = false;
                    MyLog.IsTestMode = false;
                    type = MenuType.tsmiToNormal;

                    if (DoubleClickCount % 6 == 0)
                        DoubleClickCount = 0;
                    break;
            }

            SetTestMode(type);
        }


        private static void SetState(this ToolStripMenuItem item, bool value)
        {
#if MAC
            item.State = value ? AppKit.NSCellStateValue.On : AppKit.NSCellStateValue.Off;
#else
            item.Checked = value;
#endif
        }

        private static void SetVisible(this ToolStripMenuItem item, bool value)
        {
#if MAC
            item.Hidden = !value;
#else
            item.Visible = value;
#endif
        }

#if !MAC
        private static void SetVisible(this ToolStripSeparator item, bool value)
        {
            item.Visible = value;
        }
#endif


        private static void SetMenuState()
        {
            if (MyLog.ShowLog && !MyLog.IsTestMode)
            {
                tsmiToNormal.SetState(false);
                tsmiToNormalLog.SetState(true);
                tsmiToTest.SetState(false);
                tsmiToTestLog.SetState(false);
            }
            else if (!MyLog.ShowLog && MyLog.IsTestMode)
            {
                tsmiToNormal.SetState(false);
                tsmiToNormalLog.SetState(false);
                tsmiToTest.SetState(true);
                tsmiToTestLog.SetState(false);
            }
            else if (MyLog.ShowLog && MyLog.IsTestMode)
            {
                tsmiToNormal.SetState(false);
                tsmiToNormalLog.SetState(false);
                tsmiToTest.SetState(false);
                tsmiToTestLog.SetState(true);
            }
            else
            {
                tsmiToNormal.SetState(true);
                tsmiToNormalLog.SetState(false);
                tsmiToTest.SetState(false);
                tsmiToTestLog.SetState(false);
            }
        }

        private static void SetMenuVisible()
        {
            if (MyLog.ShowLog || MyLog.IsTestMode || FirstShowMenu)
            {
                tssSplit.SetVisible(true);
                tsmiChangeMode.SetVisible(true);
                tsmiUpLogToJira.SetVisible(true);
                tsmiCreateLog.SetVisible(true);
                tsmiOpenLogFolder.SetVisible(true);
                tsmiClearLog.SetVisible(true);
                tsmiClearAll.SetVisible(true);
                tsmiOpenFpsData.SetVisible(IsWebRtcStateVisible());
               
            }
            else
            {
                tssSplit.SetVisible(false);
                tsmiChangeMode.SetVisible(false);
                tsmiUpLogToJira.SetVisible(false);
                tsmiCreateLog.SetVisible(false);
                tsmiOpenLogFolder.SetVisible(false);
                tsmiClearLog.SetVisible(false);
                tsmiClearAll.SetVisible(false);
                tsmiOpenFpsData.SetVisible(false);                
            }

            SetMenuState();
        }

        private static void InitDoubleClick()
        {
            if (MyLog.IsTestMode)
            {
                if (MyLog.ShowLog)
                    DoubleClickCount = 4;
                else
                    DoubleClickCount = 5;
            }
            else
            {
                if (MyLog.ShowLog)
                    DoubleClickCount = 3;
                else
                    DoubleClickCount = 0;
            }
        }

        public static void SetTestMode(MenuType type = MenuType.None)
        {
            TestArg e = new TestArg();
            e.Type = type;
            e.Visible = MyLog.IsTestMode || MyLog.ShowLog;

            if (type == MenuType.tsmiToNormal || type == MenuType.tsmiToNormalLog || type == MenuType.tsmiToTest || type == MenuType.tsmiToTestLog)
                e.NeedLoadUrl = (LastInTestMode != null && LastInTestMode != MyLog.IsTestMode);

            if (MyLog.IsTestMode || MyLog.ShowLog)
            {
                string strFormat = string.Empty;

                if (MyLog.IsTestMode && MyLog.ShowLog)
                {
                    strFormat = "Test And Log";
                }
                else if (MyLog.IsTestMode)
                {
                    strFormat = "Test";
                }
                else if (MyLog.ShowLog)
                {
                    strFormat = "Log";
                }

                e.Title = string.Format("In {0} Mode", strFormat);
            }

            SetMenuVisible();

            Callback?.Invoke(null, e);
        }

        public static bool IsWebRtcStateVisible()
        {
            return (Folder.AppType == RunType.RemoteSupport || Folder.AppType == RunType.BizDaemon || Folder.AppType.ToString().Contains("Cast"));
        }

#if MAC
        public static ToolStripItem CreateToolStripSeparator()
#else
        public static ToolStripSeparator CreateToolStripSeparator()
#endif
        {
#if MAC
            return NSMenuItem.SeparatorItem;
#else
            return new ToolStripSeparator();
#endif
        }

        public static void InitTestMenu(ContextMenuStrip menu)
        {
            FirstShowMenu = MyLog.ShowLog || MyLog.IsTestMode;

            InitDoubleClick();

            LanguageInterface language = LanguageInterface.Instance();
            if (!language.CurrentLanguage.LangName.ToLower().Contains("zh-"))
            {
                ToNormal = "Switch to production environment";
                ToNormalLog = "Switch to production environment log mode";
                ToTest = "Switch to test environment";
                ToTestLog = "Switch to test environment log mode";

                ChangeMode = "Switch mode";
                OpenLogFolder = "Open log directory";
                ClearLog = "Clear log files";
                CreateLog = "Package log files";
                ClearAll = "Restore default settings";
                UpLogToJira = "Upload logs to Jira";

                Warning = "Warning";
                DeleteMsg = "Are you sure you want to clear all files in the following directory?";

                OK = "OK";
                Cancel = "Cancel";

                UploadJiraTitle = "Upload logs";
                UploadJiraMsg = "Please enter the full ID of the JIRA task to upload logs to JIRA";

                UploadLogToJiraSuccessfully = "Upload logs to JIRA successfully";
                UploadLogToJiraFailed = "Upload logs to JIRA failed";
                CompressingLogFiles = "Compressing log files";
            }

            tsmiToNormal = CreateMenuItem(MenuType.tsmiToNormal, ToNormal);
            tsmiToNormalLog = CreateMenuItem(MenuType.tsmiToNormalLog, ToNormalLog);
            tsmiToTest = CreateMenuItem(MenuType.tsmiToTest, ToTest);
            tsmiToTestLog = CreateMenuItem(MenuType.tsmiToTestLog, ToTestLog);

            tssSplit = CreateToolStripSeparator();
            tsmiChangeMode = CreateMenuItem(MenuType.tsmiChangeMode, ChangeMode);
            tsmiOpenLogFolder = CreateMenuItem(MenuType.tsmiOpenLogFolder, OpenLogFolder);
            tsmiClearLog = CreateMenuItem(MenuType.tsmiClearLog, ClearLog);            
            tsmiCreateLog = CreateMenuItem(MenuType.tsmiCreateLog, CreateLog);
            tsmiClearAll = CreateMenuItem(MenuType.tsmiClearAll, ClearAll);
            tsmiUpLogToJira = CreateMenuItem(MenuType.tsmiUpLogToJira, UpLogToJira);
            tsmiOpenFpsData = CreateMenuItem(MenuType.tsmiWebRtcSate, "WebRtcState");
            

            List<ToolStripItem> list = new List<ToolStripItem>()
            {
                tssSplit,
                tsmiChangeMode,
                tsmiOpenLogFolder,
                tsmiClearLog,
                tsmiClearAll,
                tsmiCreateLog,
                tsmiUpLogToJira
            };

            if (IsWebRtcStateVisible())
                list.Add(tsmiOpenFpsData);

            List<ToolStripItem> list2 = new List<ToolStripItem>()
            {
                tsmiToNormal,
                tsmiToNormalLog,
                tsmiToTest,
                tsmiToTestLog
            };

            ListMenuItems.AddRange(list);

#if MAC
            TestMenuDelegate menuDelegate = new TestMenuDelegate();
            menuDelegate.Opening += OnMenu_Opening;
            menuDelegate.Closed += OnMenu_Closed;

            menu.Delegate = menuDelegate;

            foreach (ToolStripMenuItem item in list)
            {
                menu.AddItem(item);
            }

            ContextMenuStrip menu2 = new ContextMenuStrip();
            tsmiChangeMode.Submenu = menu2;
            foreach (ToolStripMenuItem item in list2)
            {
                menu2.AddItem(item);
            }

#else
            tsmiChangeMode.DropDownItems.AddRange(list2.ToArray());

            menu.Items.AddRange(list.ToArray());
            menu.Opening += OnMenu_Opening;
#endif

            SetTestMode();
        }

#if MAC
        private static void OnMenu_Closed(object sender, EventArgs e)
        {
            SetTestMode(MenuType.MenuClosed);
        }

        private static void OnMenu_Opening(object sender, EventArgs e)
        {
            SetTestMode(MenuType.MenuOpening);
        }
#else
        private static void OnMenu_Opening(object sender, System.ComponentModel.CancelEventArgs e)

        {
            SetTestMode(MenuType.MenuOpening);
        }
#endif

    private static void OnItemClick(object sender, EventArgs e)
        {
            ToolStripMenuItem item = sender as ToolStripMenuItem;

            string itemName = string.Empty;
#if MAC
            itemName = item.Identifier;
#else
            itemName = item.Name;
#endif

            MenuType type = (MenuType)System.Enum.Parse(typeof(MenuType), itemName);

            switch (type)
            {
                case MenuType.tsmiOpenLogFolder:
                    tsmiOpenLogFolder_Click(sender, e);
                    break;

                case MenuType.tsmiClearLog:
                    tsmiClearLog_Click(sender, e);
                    break;

                case MenuType.tsmiClearAll:
                    tsmiClearLog_Click(tsmiClearLog, EventArgs.Empty);
                    ClearSetting();
                    SetTestMode(type);
                    break;

                case MenuType.tsmiCreateLog:
                    tsmiCreateLog_Click(sender, e);
                    break;

                case MenuType.tsmiUpLogToJira:
                    tsmiUploadLogToJira_Click(sender, e);
                    break;

                case MenuType.tsmiToNormal:
                    DoubleClickCount = 0;
                    SetCurrentMode();
                    break;

                case MenuType.tsmiToNormalLog:
                    DoubleClickCount = 3;
                    SetCurrentMode();
                    break;

                case MenuType.tsmiToTest:
                    DoubleClickCount = 5;
                    SetCurrentMode();
                    break;

                case MenuType.tsmiToTestLog:
                    DoubleClickCount = 4;
                    SetCurrentMode();
                    break;

                case MenuType.tsmiWebRtcSate:
                    SetTestMode(type);
                    ShowWebRtcState();
                    break;

                default:
                    SetTestMode();
                    break;
            }
        }

        public static void ClearSetting()
        {
            if (skMessageBox.Show(string.Format("{0}\r\n{1}", DeleteMsg, Folder.ApplicationDataFolder), Warning, MessageBoxButtons.OKCancel, new string[] { OK, Cancel }) != DialogResult.OK)
                return;

            Folder.ClearFolder(Folder.ApplicationDataFolder);
        }

        public static ToolStripMenuItem CreateMenuItem(MenuType type, string itemText, bool itemChecked = false)
        {
            ToolStripMenuItem item = new ToolStripMenuItem();

            item.SetState(itemChecked);

#if MAC
            item.Identifier = type.ToString();
            item.Title = itemText;
            item.Activated += OnItemClick;
#else
            item.Name = type.ToString();
            item.Text = itemText;       
            item.Click += OnItemClick;
#endif

            return item;
        }

        private static void tsmiOpenLogFolder_Click(object sender, EventArgs e)
        {
            Common.OpenExplorer(Folder.LogFolder);
        }

        private static void tsmiClearLog_Click(object sender, EventArgs e)
        {
            Folder.ClearFolder(Folder.LogFolder.TrimEnd(Path.DirectorySeparatorChar));
        }

        private static void tsmiCreateLog_Click(object sender, EventArgs e)
        {
            ThreadMgr.Start(new ThreadStart(() =>
            {
                CreateLogZip();
            }));
        }

        public static void tsmiUploadLogToJira_Click()
        {
            tsmiUploadLogToJira_Click(tsmiUpLogToJira, EventArgs.Empty);
        }

        private static skMsgInfoNew JiraInfo = null;
        private static void tsmiUploadLogToJira_Click(object sender, EventArgs e)
        {
            if (JiraInfo != null)
                return;

            JiraInfo = GetDefaultMsgInfo(UploadJiraMsg, UploadJiraTitle, MyResource.GetIcon("airdroid.ico"));
            JiraInfo.ButtonPadding = new Padding(10, 2, 10, 0);
            JiraInfo.InputBoxInfo.Visible = true;

            if (Folder.AppType == RunType.RemoteSupport)
                JiraInfo.InputBoxInfo.skText = "ARS-";
            else if (Folder.AppType == RunType.AirDroidParentalConnector)
                JiraInfo.InputBoxInfo.skText = "AK-";
            else if (Folder.IsAirDroidPersonal)
                JiraInfo.InputBoxInfo.skText = "AD-";
            else if (Folder.IsAirDroidCast)
                JiraInfo.InputBoxInfo.skText = "AC-";
            else if (Folder.AppType.ToString().Contains("FlashGet"))
                JiraInfo.InputBoxInfo.skText = "FG-";
            else
                JiraInfo.InputBoxInfo.skText = "ADB-";

            JiraInfo = SetClearMsgInfo(JiraInfo, MyResource.GetImage("btn_clear_4.png"));

            JiraInfo.MessageInfo.Padding = new Padding(0, 0, 0, 6);
            JiraInfo.InputBoxInfo.IsPasswordMode = false;

            if (MsgBoxMgr.Show(null, JiraInfo) == DialogResult.OK)
            {
                UploadLogToJira(JiraInfo.InputBoxInfo.skText);
            }

            JiraInfo = null;
        }

        private static void UploadLogToJira(string strJiraId)
        {
            ThreadMgr.Start(new ThreadStart(() =>
            {
                skSplashBoxNew.Show(string.Format("{0} -> JiraID = {1}", CompressingLogFiles, strJiraId), null);

                bool blnResult = false;
                string filePath = string.Empty;
                string strUrl = string.Empty;
                string strLog = string.Empty;
                string token = "Basic Ym90OnhtdGIuMjAyMA==";
                if(!Folder.IsAirDroid)
                     token = "Basic SmlyYWJvdDp4bXRiLjIwMjM=";

                try
                {
                    filePath = CreateLogZip(false);
                    if (File.Exists(filePath))
                    {
                        strUrl = string.Format("http://**************:8090/rest/api/2/issue/{0}/attachments", strJiraId);

                        System.Net.WebClient client = new System.Net.WebClient();
                        client.Credentials = System.Net.CredentialCache.DefaultCredentials;
                        client.Headers.Add("Content-Type", "application/zip");
                        client.Headers.Add("Content-Type", "multipart/form-data");
                        client.Headers.Add("X-Atlassian-Token", "nocheck");
                        client.Headers.Add("Authorization", token);
                        byte[] res = client.UploadFile(new Uri(strUrl), "POST", filePath);
                        strLog = JsonParser.FormatJson(Encoding.UTF8.GetString(res));

                        blnResult = true;
                    }
                }
                catch (Exception ex)
                {
                    strLog = ex.ToString();

                    skMsgInfoNew skMsgInfo = GetDefaultMsgInfo(string.Format("{0}\r\n{1}", UploadLogToJiraFailed, ex.Message), UploadJiraTitle);
                    skMsgInfo.Buttons = MessageBoxButtons.OK;
                    MsgBoxMgr.Show(null, skMsgInfo);
                }
                finally
                {
                    MyLog.LogFile(strUrl + "\r\n" + strLog, "Jira");

                    Utility.DeleteFile(filePath);

                    if (blnResult)
                        skSplashBoxNew.Show(string.Format("{0} -> JiraID = {1}", UploadLogToJiraSuccessfully, strJiraId), null);
                }
            }));
        }

        public static string CreateLogZip(bool openFolder = true)
        {
            string zipFile = string.Empty;

            try
            {
                skDevice_Detail_Info computer = MyRS.CreateDeviceDetailInfo();
                MyJson.SerializeToJsonFile(computer, Path.Combine(Folder.LogFolder, "Device_Os_Message.txt"));

                List<string> listCopy = new List<string>();

                listCopy.AddRange(GetFileInRecenty(Folder.AppDataFolderCommon, false, new string[] { ".db", ".ini", ".dat" }, needCheckFolder: false));
                listCopy.AddRange(GetFileInRecenty(Folder.LogFolder, needCheckFolder: true));
                listCopy.AddRange(GetFileInRecenty(Folder.ExceptionFolder));
                // 移除downapp和installed的
                listCopy.RemoveAll(i => i.Contains("DownloadApp") || i.Contains("Installed"));

                string zipFolder = Folder.GetTempFolder();
                Folder.CheckFolder(zipFolder);

                foreach (string strFile in listCopy)
                {
                    try
                    {
                        File.Copy(strFile, Path.Combine(zipFolder, Path.GetFileName(strFile)), true);
                    }
                    catch
                    { }
                }

                string folder = Path.Combine(Folder.AppDataFolderCommon, "ZipCache");
                Folder.CheckFolder(folder);

                zipFile = Path.Combine(folder, (string.Format("Log_{0}.zip", DateTime.Now.ToString("yyyyMMdd_HHmmss"))));
                Utility.PackFiles(zipFile, zipFolder);

                if (openFolder)
                    Common.OpenExplorer(zipFile);
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "CreateLogZip");
            }

            return zipFile;
        }

        private static List<string> GetFileInRecenty(string dir, bool checkTime = true, string[] include = null, bool needCheckFolder = false)
        {
            List<string> listFile = new List<string>();

            dir = dir.TrimEnd(Path.DirectorySeparatorChar);

            if (Directory.Exists(dir) && !dir.EndsWith("Cache"))
            {
                string extension = "*.*";

                if (dir.Contains("CefSharp"))
                {
                    extension = "*.log";
                    needCheckFolder = false;
                }

                string[] arrFile = Directory.GetFiles(dir, extension, SearchOption.TopDirectoryOnly);
                foreach (string strFile in arrFile)
                {
                    FileInfo info = new FileInfo(strFile);

                    if (DateTime.Now.Subtract(info.CreationTime).TotalDays < 3 || DateTime.Now.Subtract(info.LastWriteTime).TotalDays < 3 || !checkTime)
                    {
                        if (include == null || (include != null && include.Contains(Path.GetExtension(strFile))))
                            listFile.Add(strFile);
                    }
                }

                if (needCheckFolder)
                {
                    string[] arrDir = Directory.GetDirectories(dir);
                    foreach (string strDir in arrDir)
                    {
                        listFile.AddRange(GetFileInRecenty(strDir, checkTime, include, needCheckFolder));
                    }
                }
            }

            return listFile;
        }

        public static skMsgInfoNew SetClearMsgInfo(skMsgInfoNew skMsgInfo, Image clearImage)
        {
            skMsgInfo.InputBoxInfo.ButtonClear = new skMsgButton
            {
                Visible = true,
                Size = new Size(16, 16),
                skIconSize = new Size(14, 14),
                skIconState = skImageState.FourState,
                Padding = new Padding(0, 0, 10, 0),
                skIcon = clearImage
            };

            int inputRightPading = (int)(2 + skMsgInfo.InputBoxInfo.ButtonClear.Size.Width + skMsgInfo.InputBoxInfo.ButtonClear.Padding.Right);
            Padding padding = skMsgInfo.InputBoxInfo.Padding;

            if (skMsgInfo.InputBoxInfo.ButtonPassword != null)
            {
                inputRightPading = (int)(inputRightPading + skMsgInfo.InputBoxInfo.ButtonPassword.Size.Width + skMsgInfo.InputBoxInfo.ButtonPassword.Padding.Right);
            }

            skMsgInfo.InputBoxInfo.Padding = new Padding(padding.Left, padding.Top, inputRightPading, padding.Bottom);


            return skMsgInfo;
        }

        public static skMsgInfoNew GetDefaultMsgInfo(string msgContent, string msgTitle, Icon titleIcon = null)
        {
            skMsgInfoNew msg = new skMsgInfoNew();

            LanguageInterface language = LanguageInterface.Instance();

            msg.Buttons = MessageBoxButtons.OKCancel;

            msg.FirstButton.skText = language.GetString("Common.OK");
            msg.SecondButton.skText = language.GetString("Common.Cancel");

            msg.FirstButton.Size = new Size(76, 28);
            msg.FirstButton.skTextFont = MyFont.CreateFont("微软雅黑", 9.75f);
            msg.FirstButton.skBackgroundImage = MyResource.GetImage("rs_btn_blue_4.png");
            msg.SecondButton.Size = new Size(76, 28);
            msg.SecondButton.skTextFont = MyFont.CreateFont("微软雅黑", 9.75f);
            msg.SecondButton.skTextColor = skColor.FromArgb(90, 94, 101);
            msg.SecondButton.skBackgroundImage = MyResource.GetImage("rs_btn_white_4.png");

            msg.MessageInfo.skText = msgContent;
            msg.MessageInfo.skTextFont = MyFont.CreateFont("微软雅黑", 9.75f);
#if MAC
            msg.MessageInfo.skTextAlign = NSTextAlignment.Left;
#else
            msg.MessageInfo.skTextAlign = ContentAlignment.TopLeft;
#endif
            msg.MessageInfo.skTextColor = skColor.FromArgb(102, 107, 117);
            msg.MessageInfo.Padding = new Padding(0, 0, 0, 18);

            //ARS-778 【RS被控端】默认弹窗文本行高修改调整间距
            if (!(language.CurrentLanguage.LangName.Contains("zh") || language.CurrentLanguage.LangName.Contains("jp")))
                msg.MessageInfo.skLineSpace = 2;

            msg.skTitleBackgroundColor = skColor.FromArgb(245, 246, 248);
            msg.skTitleColor = skColor.FromArgb(45, 47, 51);
            msg.skTitleFont = MyFont.CreateFont("微软雅黑", 9f);
            msg.skTitleBarHeight = 30;
            msg.skTitle = msgTitle;
            if (titleIcon != null)
            {
                //msg.FormIcon = titleIcon;
                //msg.IconSize = new Size(20, 20);
            }

            msg.InputBoxInfo.Size = new Size(372, 38);
            msg.InputBoxInfo.Padding = new Padding(2, 10, 2, 24);
            msg.InputBoxInfo.Margin = new Padding(2, 8, 2, 24);
            msg.InputBoxInfo.IsPasswordMode = true;
            msg.InputBoxInfo.PlaceHolder = language.GetString("Account_Password_Placeholder");
            msg.InputBoxInfo.skBorderType = skBorderType.Round;
            msg.InputBoxInfo.skBorderStrokeColor = skColor.FromArgb(234, 234, 234);
            msg.InputBoxInfo.skBorderStrokeColorFocus = skColor.FromArgb(0, 98, 246);

            msg.FormPadding = new Padding(24, 24, 24, 24);
            msg.FormMinSize = new Size(420, 0);
            msg.FormTranslateImage = MyResource.GetImage("rs_frm_bg_shadow.png");
            msg.FormIcon = MyResource.GetIcon("airdroid.ico");
            msg.ButtonPadding = new Padding(10, 0, 10, 0);

            return msg;
        }

        #region WebRtcSate

        private static System.Timers.Timer mTimer;

        private static Form frmWebRtc = null;
        private static Label mLabelState = null;
       

        /// <summary>创建显示远控数据窗体</summary>
        private static void CreateWebRtcForm()
        {
#if MAC
            NSScreen mainScreen = NSScreen.MainScreen;
            CGRect screenFrame = mainScreen.VisibleFrame;

            nfloat fixedHeight = 870;

            nfloat posX = screenFrame.Right - 420;
            nfloat posY = screenFrame.Height - fixedHeight + 30;

            frmWebRtc = new NSWindow(
                new CGRect(
                    posX,
                    posY,
                    420,
                    fixedHeight
                ),
                NSWindowStyle.Titled |
                NSWindowStyle.Closable,
                NSBackingStore.Buffered,
                false
            );

            frmWebRtc.Title = "WebRtcState";
            frmWebRtc.Level = NSWindowLevel.Floating;

            // 设置窗口标题栏背景色为白色，标题字体颜色为黑色
            frmWebRtc.TitlebarAppearsTransparent = true;
            frmWebRtc.BackgroundColor = NSColor.White;

            // 使用浅色外观，确保标题文字为黑色
            frmWebRtc.Appearance = NSAppearance.GetAppearance(NSAppearance.NameAqua);
            frmWebRtc.TitleVisibility = NSWindowTitleVisibility.Visible;

            // 使用深灰色背景
            NSColor bgColor = NSColor.FromRgb(76 / 255.0f, 76 / 255.0f, 76 / 255.0f);

            NSView contentView = new NSView(new CGRect(0, 0, 420, fixedHeight));
            contentView.WantsLayer = true;
            contentView.Layer.BackgroundColor = bgColor.CGColor;
            frmWebRtc.ContentView = contentView;

            nfloat topPadding = 15;
            nfloat bottomPadding = 30; // 增加底部间距到30像素

            NSTextView textView = new NSTextView(new CGRect(10, bottomPadding, 400, fixedHeight - topPadding - bottomPadding));
            textView.Editable = false;
            textView.Selectable = true;
            textView.TextColor = NSColor.White;
            textView.Font = NSFont.MonospacedDigitSystemFontOfSize(12, 0); // 使用等宽字体
            textView.BackgroundColor = bgColor;
            textView.AutoresizingMask = NSViewResizingMask.WidthSizable | NSViewResizingMask.HeightSizable;
            textView.Value = "-";

            mLabelState = textView;

            contentView.AddSubview(textView);

            frmWebRtc.WillClose += frmWebRtc_FormClosing;            
#else
            Screen mainScreen = Screen.PrimaryScreen;
            Rectangle rectScreen = mainScreen.WorkingArea;

            int maxHeight = 820;
            if (rectScreen.Height < maxHeight)
                maxHeight = rectScreen.Height;

            frmWebRtc = new Form();
            frmWebRtc.Size = new Size(420, maxHeight);
            frmWebRtc.StartPosition = FormStartPosition.Manual;
            frmWebRtc.Location = new Point(rectScreen.Right - frmWebRtc.Width, rectScreen.Top);
            frmWebRtc.TopMost = true;
            frmWebRtc.ShowInTaskbar = false;
            frmWebRtc.ShowIcon = false;
            frmWebRtc.Text = "WebRtcState";
            frmWebRtc.Padding = new Padding(15, 15, 15, 15);
            frmWebRtc.BackColor = Color.FromArgb(76, 76, 76);
            frmWebRtc.FormClosing += frmWebRtc_FormClosing;

            Label label = new Label();
            label.AutoSize = false;
            label.Dock = DockStyle.Fill;
            label.AutoEllipsis = true;
            label.ForeColor = Color.White;
            label.Text = "-";

            mLabelState = label;

            frmWebRtc.Controls.Add(label);
#endif
        }

#if MAC
        private static void frmWebRtc_FormClosing(object sender, EventArgs e)
        {
            CloseWebRtcState(false);
        }
#else
        private static void frmWebRtc_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (!e.Cancel)
                CloseWebRtcState(false);
        }
#endif

        /// <summary>关闭FPS数据窗口</summary>
        public static void CloseWebRtcState(bool widthClose = true)
        {
            try
            {
                if (frmWebRtc == null)
                    return;

                mTimer.Stop();
                mTimer = null;

                if (widthClose)
                    frmWebRtc.Close();

                mLabelState = null;              
                frmWebRtc = null;           
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MyTest.CloseWebRtcState");
            }
        }

        /// <summary>
        /// 显示WebRtcState数据窗体
        /// </summary>
        /// <param name="strState"></param>
        public static void ShowWebRtcState()
        {
            try
            {
                if (frmWebRtc == null)
                {
                    frmWebRtc = null;
                    CreateWebRtcForm();

#if MAC
                    frmWebRtc.MakeKeyAndOrderFront(null);
#else
                    frmWebRtc.Show();
                    frmWebRtc.Activate();
#endif

                    if (mTimer == null && Folder.AppType.ToString().Contains("Cast"))
                    {
                        mTimer = new System.Timers.Timer();
                        mTimer.Interval = 1000;
                        mTimer.Elapsed += OnTimer_Elapsed;
                    }

                    if (mTimer != null && !mTimer.Enabled)
                        mTimer.Start();
                }

                SetWebRtcState();
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MyTest.ShowWebRtcState");
            }
        }

        /// <summary>
        /// 显示WebRtcState数据窗体
        /// </summary>
        /// <param name="strState"></param>
        public static void SetWebRtcState(string strState = "-")
        {
            try
            {
#if MAC
                if (string.IsNullOrEmpty(strState) || frmWebRtc == null)
                    return;

                if (!NSThread.IsMain)
                {
                    mLabelState.BeginInvoke(new Action(() => {
                        mLabelState.Value = strState;
                    }));
                }
                else
                {
                    mLabelState.Value = strState;
                }
#else
                if (string.IsNullOrEmpty(strState) || frmWebRtc == null || frmWebRtc.IsDisposed)
                    return;

                if (mLabelState.InvokeRequired)
                {
                    mLabelState.BeginInvoke(new Action(() =>
                    {
                        mLabelState.Text = strState;
                    }));
                }
                else
                {
                    mLabelState.Text = strState;
                }
#endif
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MyTest.ShowWebRtcState");
            }
        }


        private static void GetWebRtcState()
        {
#if !MAC_FG
            try
            {
                MqttRTCClient client = MyRTC.GetShareClient(RTCMode.ScreenShare);
                if (client == null || !client.IsStarted || !client.HasICEConnected)
                {
                    if (mTimer != null)
                        mTimer.Stop();

                    return;
                }

                SetWebRtcState(client.GetFpsText(new Size(client.ScreenWidth, client.ScreenHeight)));
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MyTest.SetWebRtcState");
            }
#endif
        }

        private static void OnTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            GetWebRtcState();
        }

#endregion
    }

#if MAC
    public class TestMenuDelegate : NSMenuDelegate
    {
        public event EventHandler Opening;
        public event EventHandler Closed;
        public event EventHandler<NSMenuItem> ItemHover;

        public override void MenuWillHighlightItem(NSMenu menu, NSMenuItem item)
        {
            this.ItemHover?.Invoke(this, item);
        }

        public override void MenuWillOpen(NSMenu menu)
        {
            if (this.Opening != null)
                this.Opening(this, EventArgs.Empty);
        }

        public override void MenuDidClose(NSMenu menu)
        {
            if (this.Closed != null)
                this.Closed(this, EventArgs.Empty);
        }
    }
#endif
}
